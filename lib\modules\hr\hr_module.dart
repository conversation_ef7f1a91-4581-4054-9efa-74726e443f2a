/// وحدة الموارد البشرية الشاملة
/// Comprehensive HR Module

// Models
export 'models/hr_models.dart';

// Services
export 'services/hr_api_services.dart';

// Controllers
export 'controllers/hr_controllers.dart';

// Bindings
export 'bindings/hr_bindings.dart';

// Routes
export 'routes/hr_routes.dart';

// Screens - تم تفعيلها
export 'screens/hr_screens.dart';

// Widgets (سيتم إضافتها لاحقاً)
// export 'widgets/hr_widgets.dart';

// Utils (سيتم إضافتها لاحقاً)
// export 'utils/hr_utils.dart';

/// معلومات الوحدة
class HRModuleInfo {
  static const String name = 'HR Module';
  static const String nameArabic = 'وحدة الموارد البشرية';
  static const String version = '1.0.0';
  static const String description = 'وحدة شاملة لإدارة الموارد البشرية';
  
  /// الميزات المتاحة
  static const List<String> features = [
    'إدارة الموظفين',
    'الحضور والانصراف',
    'الرواتب والمكافآت',
    'السلف والمساعدات المالية',
    'الإجازات',
    'تقييم الأداء',
    'العهد والممتلكات',
    'المراسلات الإدارية',
    'التقارير والإحصائيات',
  ];
  
  /// الميزات المكتملة
  static const List<String> completedFeatures = [
    'نماذج البيانات',
    'خدمات API',
    'متحكمات GetX',
    'ربط التبعيات',
    'مسارات التنقل',
  ];
  
  /// الميزات قيد التطوير
  static const List<String> inProgressFeatures = [
    'واجهات المستخدم',
    'الويدجت المخصصة',
    'الأدوات المساعدة',
  ];
  
  /// الميزات المخططة
  static const List<String> plannedFeatures = [
    'تقارير PDF',
    'إشعارات تلقائية',
    'تكامل مع أجهزة البصمة',
    'لوحة تحكم تفاعلية',
    'تطبيق الجوال',
  ];
}

/// إعدادات الوحدة
class HRModuleConfig {
  /// إعدادات API
  static const String apiBaseUrl = '/api';
  static const int defaultPageSize = 10;
  static const int maxPageSize = 100;
  
  /// إعدادات الحضور
  static const Duration workDayDuration = Duration(hours: 8);
  static const Duration lateThreshold = Duration(minutes: 15);
  static const Duration earlyLeaveThreshold = Duration(minutes: 30);
  
  /// إعدادات الإجازات
  static const Map<String, int> defaultLeaveAllowances = {
    'annual': 30,
    'sick': 15,
    'emergency': 5,
    'maternity': 90,
    'unpaid': 0,
  };
  
  /// إعدادات التقارير
  static const List<String> supportedExportFormats = [
    'excel',
    'csv',
    'pdf',
  ];
  
  /// إعدادات الألوان
  static const Map<String, String> statusColors = {
    'active': '#4CAF50',
    'inactive': '#FF9800',
    'terminated': '#F44336',
    'pending': '#2196F3',
    'approved': '#4CAF50',
    'rejected': '#F44336',
    'cancelled': '#9E9E9E',
  };
}

/// مساعد تهيئة الوحدة
class HRModuleInitializer {
  /// تهيئة الوحدة
  static Future<void> initialize() async {
    try {
      // تسجيل المسارات
      _registerRoutes();
      
      // تهيئة الخدمات
      await _initializeServices();
      
      // تهيئة المتحكمات
      _initializeControllers();
      
      print('✅ تم تهيئة وحدة الموارد البشرية بنجاح');
    } catch (e) {
      print('❌ خطأ في تهيئة وحدة الموارد البشرية: $e');
      rethrow;
    }
  }
  
  /// تسجيل المسارات
  static void _registerRoutes() {
    // سيتم تنفيذ هذا عند إنشاء الشاشات
    print('📍 تم تسجيل مسارات الموارد البشرية');
  }
  
  /// تهيئة الخدمات
  static Future<void> _initializeServices() async {
    // تهيئة خدمات API
    print('🔧 تم تهيئة خدمات API للموارد البشرية');
  }
  
  /// تهيئة المتحكمات
  static void _initializeControllers() {
    // تسجيل المتحكمات في GetX
    print('🎮 تم تهيئة متحكمات الموارد البشرية');
  }
}

/// مساعد التحقق من الصلاحيات
class HRPermissionHelper {
  /// التحقق من صلاحية الوصول للموظفين
  static bool canAccessEmployees() {
    // TODO: تنفيذ التحقق من الصلاحيات
    return true;
  }
  
  /// التحقق من صلاحية إدارة الموظفين
  static bool canManageEmployees() {
    // TODO: تنفيذ التحقق من الصلاحيات
    return true;
  }
  
  /// التحقق من صلاحية الوصول للحضور
  static bool canAccessAttendance() {
    // TODO: تنفيذ التحقق من الصلاحيات
    return true;
  }
  
  /// التحقق من صلاحية إدارة الحضور
  static bool canManageAttendance() {
    // TODO: تنفيذ التحقق من الصلاحيات
    return true;
  }
  
  /// التحقق من صلاحية الوصول للرواتب
  static bool canAccessPayroll() {
    // TODO: تنفيذ التحقق من الصلاحيات
    return true;
  }
  
  /// التحقق من صلاحية إدارة الرواتب
  static bool canManagePayroll() {
    // TODO: تنفيذ التحقق من الصلاحيات
    return true;
  }
  
  /// التحقق من صلاحية الوصول للإجازات
  static bool canAccessLeaves() {
    // TODO: تنفيذ التحقق من الصلاحيات
    return true;
  }
  
  /// التحقق من صلاحية إدارة الإجازات
  static bool canManageLeaves() {
    // TODO: تنفيذ التحقق من الصلاحيات
    return true;
  }
  
  /// التحقق من صلاحية الوصول للتقارير
  static bool canAccessReports() {
    // TODO: تنفيذ التحقق من الصلاحيات
    return true;
  }
  
  /// التحقق من صلاحية إنشاء التقارير
  static bool canCreateReports() {
    // TODO: تنفيذ التحقق من الصلاحيات
    return true;
  }
}
