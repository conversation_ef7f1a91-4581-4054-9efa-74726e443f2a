{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["5K2qlktR6XCQ2xo8vF7wga2f1D8u0ErKsolAeamD85E=", "m3BaUu/Teg65JoCOpFf7YqvqOehyxvRBgCr+/8XWcRM=", "I9dZDQbjWxnHG81xtvLpArCw6uw2IRs7rDZOzVprUik=", "Qg52zDeTjPB4a6yajdjwvrTkD2YtanC1YSE6vVdHgwA=", "8lobZuJfYq+Yu/YNTtp1sX+ZmFncB6x1evA70KUAGPs=", "1dI3NehK4vVYBVheOhkbMLmQ+woM5675vg4J8QGyuAA="], "CachedAssets": {"I9dZDQbjWxnHG81xtvLpArCw6uw2IRs7rDZOzVprUik=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\f2eqwau0gr-ecem5x2v5n.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/8f6873cdd5814931959cd8b81db2d7fa#[.{fingerprint=ecem5x2v5n}]?.csv.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\8f6873cdd5814931959cd8b81db2d7fa.csv", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lcfl19kylj", "Integrity": "C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\8f6873cdd5814931959cd8b81db2d7fa.csv", "FileLength": 756, "LastWriteTime": "2025-08-08T15:18:24.2790659+00:00"}, "m3BaUu/Teg65JoCOpFf7YqvqOehyxvRBgCr+/8XWcRM=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ad4wzmw55x-je3vbe9eb6.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/3f3113bbbb4d489b83401dff8c0fdfdd#[.{fingerprint=je3vbe9eb6}]?.csv.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\3f3113bbbb4d489b83401dff8c0fdfdd.csv", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qva2e0zyb4", "Integrity": "at5YydYhql7IaKEFsOXJvgNUvkegiGcimAq420+jGOo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\3f3113bbbb4d489b83401dff8c0fdfdd.csv", "FileLength": 5246, "LastWriteTime": "2025-08-08T15:18:24.280067+00:00"}, "5K2qlktR6XCQ2xo8vF7wga2f1D8u0ErKsolAeamD85E=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\xoelc6sdnq-dz1rjozczi.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2)#[.{fingerprint=dz1rjo<PERSON>cz<PERSON>}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "00icdbvmee", "Integrity": "II89AIAfepVyxMAEBSxXRGF+cpbNgb15nTDmDlReLDI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "FileLength": 7549, "LastWriteTime": "2025-08-08T15:18:24.2841723+00:00"}, "Qg52zDeTjPB4a6yajdjwvrTkD2YtanC1YSE6vVdHgwA=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\nr15fqvel8-ecem5x2v5n.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/a85476dcc9b944f48a6c942f9ffe5e76#[.{fingerprint=ecem5x2v5n}]?.csv.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\a85476dcc9b944f48a6c942f9ffe5e76.csv", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lcfl19kylj", "Integrity": "C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\a85476dcc9b944f48a6c942f9ffe5e76.csv", "FileLength": 756, "LastWriteTime": "2025-08-08T15:18:24.2790659+00:00"}, "8lobZuJfYq+Yu/YNTtp1sX+ZmFncB6x1evA70KUAGPs=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ph3rqhnt6w-knc7nr4hqg.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca#[.{fingerprint=knc7nr4hqg}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "72uezgg7hx", "Integrity": "UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "FileLength": 1927, "LastWriteTime": "2025-08-08T15:18:24.2790659+00:00"}, "1dI3NehK4vVYBVheOhkbMLmQ+woM5675vg4J8QGyuAA=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\v25brddiup-knc7nr4hqg.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e#[.{fingerprint=knc7nr4hqg}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "72uezgg7hx", "Integrity": "UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "FileLength": 1927, "LastWriteTime": "2025-08-08T15:18:24.2790659+00:00"}}, "CachedCopyCandidates": {}}