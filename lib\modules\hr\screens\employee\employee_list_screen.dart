import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import '../../controllers/hr_controllers.dart';
import '../../routes/hr_routes.dart';
import '../../widgets/employee_data_source.dart';
import '../../../../controllers/department_controller.dart';

/// شاشة قائمة الموظفين مع جدول Syncfusion
class EmployeeListScreen extends StatefulWidget {
  const EmployeeListScreen({super.key});

  @override
  State<EmployeeListScreen> createState() => _EmployeeListScreenState();
}

class _EmployeeListScreenState extends State<EmployeeListScreen> {
  late EmployeeDataSource _employeeDataSource;
  late EmployeeController controller;
  late DepartmentController departmentController;
  final DataGridController _dataGridController = DataGridController();

  @override
  void initState() {
    super.initState();
    controller = Get.find<EmployeeController>();

    // تسجيل DepartmentController إذا لم يكن مُسجلاً
    if (!Get.isRegistered<DepartmentController>()) {
      Get.lazyPut(() => DepartmentController());
    }
    departmentController = Get.find<DepartmentController>();

    _employeeDataSource = EmployeeDataSource(employees: []);

    // تحميل البيانات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.loadEmployees(refresh: true);
      departmentController.loadAllDepartments(); // تحميل الأقسام
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الموظفين'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          // زر البحث
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
          // زر الفلترة
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
          ),
          // زر التصدير
          PopupMenuButton<String>(
            onSelected: (value) => _handleExport(value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'excel',
                child: ListTile(
                  leading: Icon(Icons.table_chart),
                  title: Text('تصدير إلى Excel'),
                ),
              ),
              const PopupMenuItem(
                value: 'pdf',
                child: ListTile(
                  leading: Icon(Icons.picture_as_pdf),
                  title: Text('تصدير إلى PDF'),
                ),
              ),
            ],
          ),
          // زر التحديث
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.loadEmployees(refresh: true),
          ),
        ],
      ),
      body: Obx(() {
        // تحديث مصدر البيانات عند تغيير قائمة الموظفين
        _employeeDataSource.updateDataSource(controller.employees);

        if (controller.isLoading.value && controller.employees.isEmpty) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.error.value.isNotEmpty) {
          return _buildErrorState();
        }

        if (controller.employees.isEmpty) {
          return _buildEmptyState();
        }

        return Column(
          children: [
            // شريط الإحصائيات السريعة
            _buildQuickStats(),
            // الجدول
            Expanded(
              child: SfDataGrid(
                source: _employeeDataSource,
                controller: _dataGridController,
                allowSorting: true,
                allowFiltering: true,
                allowColumnsResizing: true,
                columnResizeMode: ColumnResizeMode.onResize,
                gridLinesVisibility: GridLinesVisibility.both,
                headerGridLinesVisibility: GridLinesVisibility.both,
                selectionMode: SelectionMode.single,
                navigationMode: GridNavigationMode.cell,
                columnWidthMode: ColumnWidthMode.auto,
                columns: _buildColumns(),
                onCellTap: (details) {
                  if (details.rowColumnIndex.rowIndex > 0) {
                    final employee = controller.employees[details.rowColumnIndex.rowIndex - 1];
                    HRNavigator.toEmployeeDetails(employee.id);
                  }
                },
              ),
            ),
            // شريط التحميل للمزيد
            if (controller.isLoadingMore.value)
              const Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),
          ],
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: () => HRNavigator.toAddEmployee(),
        backgroundColor: Theme.of(context).primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  /// بناء أعمدة الجدول
  List<GridColumn> _buildColumns() {
    return [
      GridColumn(
        columnName: 'avatar',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('الصورة', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 80,
      ),
      GridColumn(
        columnName: 'name',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('الاسم', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 150,
      ),
      GridColumn(
        columnName: 'employeeId',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('رقم الموظف', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'email',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('البريد الإلكتروني', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 200,
      ),
      GridColumn(
        columnName: 'jobTitle',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('المنصب', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 150,
      ),
      GridColumn(
        columnName: 'department',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('القسم', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'hireDate',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('تاريخ التوظيف', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'status',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('الحالة', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 100,
      ),
      GridColumn(
        columnName: 'age',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('العمر', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 80,
      ),
      GridColumn(
        columnName: 'experience',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('سنوات الخبرة', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 100,
      ),
      GridColumn(
        columnName: 'governorate',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('المحافظة', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'qualification',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('المؤهل', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'birthPlace',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('مكان الميلاد', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'bloodType',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('فصيلة الدم', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 100,
      ),
      GridColumn(
        columnName: 'address',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('العنوان', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 200,
      ),
      GridColumn(
        columnName: 'directorate',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('المديرية', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'salaryGrade',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('الدرجة الوظيفية', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'hobbies',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('الهوايات', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 150,
      ),
      GridColumn(
        columnName: 'firstName',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('الاسم الأول', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'lastName',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('الاسم الأخير', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'birthDate',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('تاريخ الميلاد', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'isolation',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('العزلة', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 100,
      ),
      GridColumn(
        columnName: 'village',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('القرية', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 100,
      ),
      GridColumn(
        columnName: 'skills',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('المهارات', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 200,
      ),
      GridColumn(
        columnName: 'employeeNotes',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('الملاحظات', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 200,
      ),
      GridColumn(
        columnName: 'role',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('الدور', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'isOnline',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('حالة الاتصال', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'phone',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('رقم الهاتف', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'isActive',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('حالة النشاط', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 100,
      ),
      GridColumn(
        columnName: 'createdAt',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('تاريخ الإنشاء', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'lastLogin',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('آخر تسجيل دخول', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 140,
      ),
      GridColumn(
        columnName: 'lastSeen',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('آخر ظهور', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'twoFactorEnabled',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('المصادقة الثنائية', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'employeeUpdatedAt',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('آخر تحديث', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'actions',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: const Text('الإجراءات', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        width: 140, // زيادة العرض لاستيعاب 3 أزرار
        minimumWidth: 120,
        maximumWidth: 160,
        allowSorting: false,
        allowFiltering: false,
      ),
    ];
  }

  /// بناء شريط الإحصائيات السريعة
  Widget _buildQuickStats() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[100],
      child: Row(
        children: [
          _buildStatCard('إجمالي الموظفين', controller.totalCount.value.toString(), Colors.blue),
          const SizedBox(width: 16),
          _buildStatCard('النشطين', _getActiveCount().toString(), Colors.green),
          const SizedBox(width: 16),
          _buildStatCard('غير النشطين', _getInactiveCount().toString(), Colors.orange),
          const SizedBox(width: 16),
          _buildStatCard('منتهي الخدمة', _getTerminatedCount().toString(), Colors.red),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// حساب عدد الموظفين النشطين
  int _getActiveCount() {
    return controller.employees.where((emp) => emp.employmentStatus == 'active').length;
  }

  /// حساب عدد الموظفين غير النشطين
  int _getInactiveCount() {
    return controller.employees.where((emp) => emp.employmentStatus == 'inactive').length;
  }

  /// حساب عدد الموظفين منتهي الخدمة
  int _getTerminatedCount() {
    return controller.employees.where((emp) => emp.employmentStatus == 'terminated').length;
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            controller.error.value,
            style: TextStyle(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => controller.loadEmployees(refresh: true),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.people_outline, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          const Text(
            'لا توجد موظفين',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
          const SizedBox(height: 8),
          const Text(
            'اضغط على زر + لإضافة موظف جديد',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => HRNavigator.toAddEmployee(),
            icon: const Icon(Icons.add),
            label: const Text('إضافة موظف جديد'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار البحث
  void _showSearchDialog(BuildContext context) {
    final searchController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('البحث في الموظفين'),
        content: TextField(
          controller: searchController,
          decoration: const InputDecoration(
            hintText: 'ادخل اسم الموظف أو رقم الموظف',
            prefixIcon: Icon(Icons.search),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              controller.searchEmployees(searchController.text);
              Get.back();
            },
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار الفلترة
  void _showFilterDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        title: const Text('فلترة الموظفين'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // فلتر القسم
            Obx(() => DropdownButtonFormField<int?>(
              decoration: InputDecoration(
                labelText: 'القسم',
                suffixIcon: departmentController.isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : null,
              ),
              value: controller.selectedDepartmentId.value,
              items: [
                const DropdownMenuItem(value: null, child: Text('جميع الأقسام')),
                ...departmentController.activeDepartments.map(
                  (dept) => DropdownMenuItem(
                    value: dept.id,
                    child: Text(
                      '${'  ' * dept.level}${dept.name}', // مسافة بادئة حسب المستوى
                      style: TextStyle(
                        fontWeight: dept.level == 0 ? FontWeight.bold : FontWeight.normal,
                        color: dept.level == 0 ? Colors.blue[700] : null,
                      ),
                    ),
                  ),
                ),
              ],
              onChanged: departmentController.isLoading
                ? null
                : (value) => controller.filterByDepartment(value),
            )),
            const SizedBox(height: 16),
            // فلتر حالة التوظيف
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(labelText: 'حالة التوظيف'),
              value: controller.selectedEmploymentStatus.value.isEmpty ? null : controller.selectedEmploymentStatus.value,
              items: const [
                DropdownMenuItem(value: null, child: Text('جميع الحالات')),
                DropdownMenuItem(value: 'active', child: Text('نشط')),
                DropdownMenuItem(value: 'inactive', child: Text('غير نشط')),
                DropdownMenuItem(value: 'terminated', child: Text('منتهي الخدمة')),
              ],
              onChanged: (value) => controller.filterByEmploymentStatus(value ?? ''),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              controller.clearFilters();
              Get.back();
            },
            child: const Text('مسح الفلاتر'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(),
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  /// معالجة التصدير
  void _handleExport(String type) {
    switch (type) {
      case 'excel':
        _exportToExcel();
        break;
      case 'pdf':
        _exportToPdf();
        break;
    }
  }

  /// تصدير إلى Excel
  void _exportToExcel() {
    // TODO: تطبيق تصدير Excel
    Get.snackbar(
      'تصدير',
      'سيتم إضافة وظيفة التصدير إلى Excel قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// تصدير إلى PDF
  void _exportToPdf() {
    // TODO: تطبيق تصدير PDF
    Get.snackbar(
      'تصدير',
      'سيتم إضافة وظيفة التصدير إلى PDF قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  @override
  void dispose() {
    _dataGridController.dispose();
    super.dispose();
  }
}
