import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../services/hr_api_services.dart';

/// متحكم تاريخ العمل والخبرات المهنية
class WorkHistoryController extends GetxController {
  final WorkHistoryApiService _apiService = WorkHistoryApiService();

  // Observable variables
  final RxList<WorkHistory> workHistoryList = <WorkHistory>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool isLoadingMore = false.obs;
  final RxString error = ''.obs;
  final RxString message = ''.obs;
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final RxInt totalCount = 0.obs;
  final RxBool hasMoreData = true.obs;

  // Search and filter
  final RxString searchTerm = ''.obs;
  final RxnInt selectedEmployeeId = RxnInt();
  final RxString selectedOrganizationType = ''.obs;
  final RxnBool selectedIsCurrent = RxnBool();
  final RxString sortBy = 'StartDate'.obs;
  final RxString sortDirection = 'desc'.obs;

  // Statistics
  final Rxn<WorkHistoryStatisticsDto> statistics = Rxn<WorkHistoryStatisticsDto>();

  // Selected work history for details
  final Rxn<WorkHistory> selectedWorkHistory = Rxn<WorkHistory>();

  // Employee work history (for specific employee)
  final RxList<WorkHistory> employeeWorkHistory = <WorkHistory>[].obs;

  @override
  void onInit() {
    super.onInit();
    _initializeApiService();
    loadWorkHistory();
  }

  /// تهيئة خدمة API
  Future<void> _initializeApiService() async {
    try {
      await _apiService.initialize();
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة API لتاريخ العمل: $e');
    }
  }

  /// تحميل تاريخ العمل
  Future<void> loadWorkHistory({bool refresh = false}) async {
    try {
      if (refresh) {
        currentPage.value = 1;
        hasMoreData.value = true;
        workHistoryList.clear();
      }

      if (!hasMoreData.value && !refresh) return;

      if (currentPage.value == 1) {
        isLoading.value = true;
      } else {
        isLoadingMore.value = true;
      }

      error.value = '';
      message.value = '';

      final result = await _apiService.getAllWorkHistory(
        page: currentPage.value,
        pageSize: 10,
        searchTerm: searchTerm.value.isEmpty ? null : searchTerm.value,
        employeeId: selectedEmployeeId.value,
        organizationType: selectedOrganizationType.value.isEmpty ? null : selectedOrganizationType.value,
        isCurrent: selectedIsCurrent.value,
      );

      final newWorkHistory = result['workHistory'] as List<WorkHistory>;
      totalCount.value = result['totalCount'] as int;
      totalPages.value = result['totalPages'] as int;

      // إذا كانت هناك رسالة من API، احفظها
      if (result['message'] != null) {
        message.value = result['message'] as String;
        debugPrint('📝 رسالة من API: ${message.value}');
      }

      if (refresh) {
        workHistoryList.assignAll(newWorkHistory);
      } else {
        workHistoryList.addAll(newWorkHistory);
      }

      hasMoreData.value = currentPage.value < totalPages.value;
      if (hasMoreData.value) {
        currentPage.value++;
      }

    } catch (e) {
      error.value = 'خطأ في تحميل تاريخ العمل: $e';
      debugPrint('خطأ في تحميل تاريخ العمل: $e');
    } finally {
      isLoading.value = false;
      isLoadingMore.value = false;
    }
  }

  /// تحميل تاريخ العمل لموظف محدد
  Future<void> loadEmployeeWorkHistory(int employeeId) async {
    try {
      isLoading.value = true;
      error.value = '';

      final workHistory = await _apiService.getEmployeeWorkHistory(employeeId);
      employeeWorkHistory.assignAll(workHistory);

    } catch (e) {
      error.value = 'خطأ في تحميل تاريخ عمل الموظف: $e';
      debugPrint('خطأ في تحميل تاريخ عمل الموظف: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل المزيد من سجلات تاريخ العمل
  Future<void> loadMoreWorkHistory() async {
    if (!isLoadingMore.value && hasMoreData.value) {
      await loadWorkHistory();
    }
  }

  /// البحث في تاريخ العمل
  Future<void> searchWorkHistory(String term) async {
    searchTerm.value = term;
    await loadWorkHistory(refresh: true);
  }

  /// فلترة حسب الموظف
  Future<void> filterByEmployee(int? employeeId) async {
    selectedEmployeeId.value = employeeId;
    debugPrint('🔍 تم تحديد الموظف: $employeeId');
    await loadWorkHistory(refresh: true);
  }

  /// فلترة حسب نوع المنظمة
  Future<void> filterByOrganizationType(String organizationType) async {
    selectedOrganizationType.value = organizationType;
    await loadWorkHistory(refresh: true);
  }

  /// فلترة حسب حالة العمل (حالي/سابق)
  Future<void> filterByCurrentStatus(bool? isCurrent) async {
    selectedIsCurrent.value = isCurrent;
    await loadWorkHistory(refresh: true);
  }

  /// ترتيب تاريخ العمل
  Future<void> sortWorkHistory(String field, String direction) async {
    sortBy.value = field;
    sortDirection.value = direction;
    await loadWorkHistory(refresh: true);
  }

  /// مسح الفلاتر
  Future<void> clearFilters() async {
    searchTerm.value = '';
    selectedEmployeeId.value = null;
    selectedOrganizationType.value = '';
    selectedIsCurrent.value = null;
    sortBy.value = 'StartDate';
    sortDirection.value = 'desc';
    await loadWorkHistory(refresh: true);
  }

  /// التحقق من صحة الموظف المحدد وإصلاحه إذا لزم الأمر
  void validateSelectedEmployee(List<dynamic> availableEmployees) {
    if (selectedEmployeeId.value != null) {
      final employeeExists = availableEmployees.any((emp) =>
        (emp is Map && emp['id'] == selectedEmployeeId.value) ||
        (emp.id == selectedEmployeeId.value)
      );

      if (!employeeExists) {
        debugPrint('⚠️ الموظف المحدد (${selectedEmployeeId.value}) غير موجود في القائمة، سيتم مسح الاختيار');
        selectedEmployeeId.value = null;
      }
    }
  }

  /// الحصول على تفاصيل سجل عمل
  Future<void> getWorkHistoryDetails(int id) async {
    try {
      isLoading.value = true;
      error.value = '';

      final workHistory = await _apiService.getWorkHistoryDetails(id);
      selectedWorkHistory.value = workHistory;

    } catch (e) {
      error.value = 'خطأ في الحصول على تفاصيل سجل العمل: $e';
      debugPrint('خطأ في الحصول على تفاصيل سجل العمل: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// إنشاء سجل عمل جديد
  Future<bool> createWorkHistory(CreateWorkHistoryDto dto) async {
    try {
      isLoading.value = true;
      error.value = '';

      final newWorkHistory = await _apiService.createWorkHistory(dto);
      workHistoryList.insert(0, newWorkHistory);
      totalCount.value++;

      // إضافة للقائمة الخاصة بالموظف إذا كانت محملة
      if (selectedEmployeeId.value == dto.employeeId) {
        employeeWorkHistory.insert(0, newWorkHistory);
      }

      Get.snackbar(
        'نجح',
        'تم إضافة سجل العمل بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      return true;
    } catch (e) {
      error.value = 'خطأ في إنشاء سجل العمل: $e';
      Get.snackbar(
        'خطأ',
        'فشل في إضافة سجل العمل: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// تحديث سجل العمل
  Future<bool> updateWorkHistory(int id, UpdateWorkHistoryDto dto) async {
    try {
      isLoading.value = true;
      error.value = '';

      final updatedWorkHistory = await _apiService.updateWorkHistory(id, dto);
      
      // تحديث في القائمة الرئيسية
      final index = workHistoryList.indexWhere((wh) => wh.id == id);
      if (index != -1) {
        workHistoryList[index] = updatedWorkHistory;
      }

      // تحديث في قائمة الموظف
      final empIndex = employeeWorkHistory.indexWhere((wh) => wh.id == id);
      if (empIndex != -1) {
        employeeWorkHistory[empIndex] = updatedWorkHistory;
      }

      // تحديث المحدد إذا كان نفسه
      if (selectedWorkHistory.value?.id == id) {
        selectedWorkHistory.value = updatedWorkHistory;
      }

      Get.snackbar(
        'نجح',
        'تم تحديث سجل العمل بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      return true;
    } catch (e) {
      error.value = 'خطأ في تحديث سجل العمل: $e';
      Get.snackbar(
        'خطأ',
        'فشل في تحديث سجل العمل: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// حذف سجل العمل
  Future<bool> deleteWorkHistory(int id) async {
    try {
      isLoading.value = true;
      error.value = '';

      await _apiService.deleteWorkHistory(id);

      // إزالة من القائمة الرئيسية
      workHistoryList.removeWhere((wh) => wh.id == id);
      totalCount.value--;

      // إزالة من قائمة الموظف
      employeeWorkHistory.removeWhere((wh) => wh.id == id);

      // مسح المحدد إذا كان نفسه
      if (selectedWorkHistory.value?.id == id) {
        selectedWorkHistory.value = null;
      }

      Get.snackbar(
        'نجح',
        'تم حذف سجل العمل بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      return true;
    } catch (e) {
      error.value = 'خطأ في حذف سجل العمل: $e';
      Get.snackbar(
        'خطأ',
        'فشل في حذف سجل العمل: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل إحصائيات تاريخ العمل
  Future<void> loadStatistics() async {
    try {
      isLoading.value = true;
      error.value = '';

      final stats = await _apiService.getWorkHistoryStatistics();
      statistics.value = stats;

    } catch (e) {
      error.value = 'خطأ في تحميل الإحصائيات: $e';
      debugPrint('خطأ في تحميل الإحصائيات: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تصدير بيانات تاريخ العمل
  Future<String?> exportWorkHistory({
    String format = 'excel',
    int? employeeId,
    String? organizationType,
    bool? isCurrent,
  }) async {
    try {
      isLoading.value = true;
      error.value = '';

      final downloadUrl = await _apiService.exportWorkHistory(
        format: format,
        employeeId: employeeId,
        organizationType: organizationType,
        isCurrent: isCurrent,
      );

      Get.snackbar(
        'نجح',
        'تم تصدير البيانات بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      return downloadUrl;
    } catch (e) {
      error.value = 'خطأ في تصدير البيانات: $e';
      Get.snackbar(
        'خطأ',
        'فشل في تصدير البيانات: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// الحصول على أنواع المنظمات
  List<Map<String, String>> getOrganizationTypes() {
    return _apiService.getOrganizationTypes();
  }

  /// الحصول على أسباب ترك العمل
  List<Map<String, String>> getLeavingReasons() {
    return _apiService.getLeavingReasons();
  }

  /// إعادة تعيين الحالة
  void resetState() {
    workHistoryList.clear();
    employeeWorkHistory.clear();
    selectedWorkHistory.value = null;
    statistics.value = null;
    error.value = '';
    currentPage.value = 1;
    totalPages.value = 1;
    totalCount.value = 0;
    hasMoreData.value = true;
    searchTerm.value = '';
    selectedEmployeeId.value = null;
    selectedOrganizationType.value = '';
    selectedIsCurrent.value = null;
  }

  /// تحديث سجل عمل في القائمة
  void updateWorkHistoryInList(WorkHistory updatedWorkHistory) {
    final index = workHistoryList.indexWhere((wh) => wh.id == updatedWorkHistory.id);
    if (index != -1) {
      workHistoryList[index] = updatedWorkHistory;
    }
  }

  /// إضافة سجل عمل جديد للقائمة
  void addWorkHistoryToList(WorkHistory newWorkHistory) {
    workHistoryList.insert(0, newWorkHistory);
    totalCount.value++;
  }

  /// حساب إجمالي سنوات الخبرة لموظف
  double calculateTotalExperience(List<WorkHistory> workHistory) {
    double totalYears = 0;
    for (final work in workHistory) {
      totalYears += work.durationInYears;
    }
    return totalYears;
  }

  /// الحصول على العمل الحالي لموظف
  WorkHistory? getCurrentJob(List<WorkHistory> workHistory) {
    try {
      return workHistory.firstWhere((work) => work.isCurrent);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على آخر عمل لموظف
  WorkHistory? getLatestJob(List<WorkHistory> workHistory) {
    if (workHistory.isEmpty) return null;

    final sortedHistory = List<WorkHistory>.from(workHistory);
    sortedHistory.sort((a, b) => b.startDate.compareTo(a.startDate));
    return sortedHistory.first;
  }
}
