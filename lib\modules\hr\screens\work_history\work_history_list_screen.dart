import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import '../../controllers/hr_controllers.dart';
import '../../models/hr_models.dart';
import '../../../../constants/app_colors.dart';
import '../../../../constants/app_styles.dart';
import '../../../../widgets/loading_indicator.dart';


/// شاشة قائمة تاريخ العمل والخبرات المهنية
class WorkHistoryListScreen extends StatefulWidget {
  const WorkHistoryListScreen({super.key});

  @override
  State<WorkHistoryListScreen> createState() => _WorkHistoryListScreenState();
}

class _WorkHistoryListScreenState extends State<WorkHistoryListScreen> {
  late WorkHistoryController controller;
  late EmployeeController employeeController;
  final DataGridController _dataGridController = DataGridController();

  @override
  void initState() {
    super.initState();
    
    // تسجيل المتحكمات إذا لم تكن مُسجلة
    if (!Get.isRegistered<WorkHistoryController>()) {
      Get.lazyPut(() => WorkHistoryController());
    }
    if (!Get.isRegistered<EmployeeController>()) {
      Get.lazyPut(() => EmployeeController());
    }
    controller = Get.find<WorkHistoryController>();
    employeeController = Get.find<EmployeeController>();

    // تحميل البيانات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      employeeController.loadEmployees(); // تحميل الموظفين أولاً

      // التحقق من صحة الموظف المحدد بعد تحميل قائمة الموظفين
      ever(employeeController.employees, (employees) {
        controller.validateSelectedEmployee(employees);
      });

      controller.loadWorkHistory(refresh: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تاريخ العمل والخبرات'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          // زر البحث
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
          // زر الفلترة مع مؤشر
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.filter_list),
                onPressed: () => _showFilterDialog(context),
              ),
              // مؤشر الفلاتر النشطة
              if (_hasActiveFilters())
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
            ],
          ),
          // زر التصدير
          PopupMenuButton<String>(
            onSelected: (value) => _handleExport(value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'excel',
                child: ListTile(
                  leading: Icon(Icons.table_chart),
                  title: Text('تصدير إلى Excel'),
                ),
              ),
              const PopupMenuItem(
                value: 'pdf',
                child: ListTile(
                  leading: Icon(Icons.picture_as_pdf),
                  title: Text('تصدير إلى PDF'),
                ),
              ),
            ],
          ),
          // زر التحديث
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.loadWorkHistory(refresh: true),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value && controller.workHistoryList.isEmpty) {
          return const LoadingIndicator();
        }

        if (controller.error.value.isNotEmpty) {
          return _buildErrorState();
        }

        if (controller.workHistoryList.isEmpty) {
          return _buildEmptyState();
        }

        return Column(
          children: [
            // شريط الإحصائيات السريعة
            _buildQuickStats(),

            // شريط معلومات النتائج والفلاتر
            if (_hasActiveFilters() || controller.totalCount.value > 0)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                color: Colors.grey[100],
                child: Row(
                  children: [
                    Icon(Icons.info_outline, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _getFilterInfoText(),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                    if (_hasActiveFilters())
                      TextButton(
                        onPressed: () => controller.clearFilters(),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          minimumSize: const Size(0, 32),
                        ),
                        child: const Text(
                          'مسح الفلاتر',
                          style: TextStyle(fontSize: 12),
                        ),
                      ),
                  ],
                ),
              ),

            // قائمة تاريخ العمل
            Expanded(
              child: _buildWorkHistoryList(),
            ),
            // شريط التحميل للمزيد
            if (controller.isLoadingMore.value)
              const Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),
          ],
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          debugPrint('🔘 تم الضغط على زر إضافة سجل عمل جديد');
          _navigateToAddWorkHistory();
        },
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  /// بناء شريط الإحصائيات السريعة
  Widget _buildQuickStats() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[100],
      child: Row(
        children: [
          _buildStatCard('إجمالي السجلات', controller.totalCount.value.toString(), Colors.blue),
          const SizedBox(width: 16),
          _buildStatCard('الوظائف الحالية', _getCurrentJobsCount().toString(), Colors.green),
          const SizedBox(width: 16),
          _buildStatCard('الوظائف السابقة', _getPreviousJobsCount().toString(), Colors.orange),
          const SizedBox(width: 16),
          _buildStatCard('متوسط المدة', _getAverageDuration(), Colors.purple),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 11),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء قائمة تاريخ العمل
  Widget _buildWorkHistoryList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: controller.workHistoryList.length + (controller.hasMoreData.value ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == controller.workHistoryList.length) {
          // عنصر التحميل في النهاية
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: CircularProgressIndicator(),
            ),
          );
        }

        final workHistory = controller.workHistoryList[index];
        return _buildWorkHistoryCard(workHistory);
      },
    );
  }

  /// بناء بطاقة سجل العمل
  Widget _buildWorkHistoryCard(WorkHistory workHistory) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _navigateToWorkHistoryDetails(workHistory.id),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف الأول: المنصب والشركة
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          workHistory.jobTitle,
                          style: AppStyles.titleMedium.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          workHistory.companyName,
                          style: AppStyles.bodyMedium.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // حالة العمل
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: workHistory.isCurrent ? Colors.green : Colors.grey,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      workHistory.statusText,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // الصف الثاني: معلومات إضافية
              Row(
                children: [
                  // اسم الموظف
                  if (workHistory.employeeName != null) ...[
                    Icon(Icons.person, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      workHistory.employeeName!,
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    const SizedBox(width: 16),
                  ],
                  
                  // نوع المنظمة
                  if (workHistory.organizationType != null) ...[
                    Icon(Icons.business, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      workHistory.organizationTypeArabic,
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    const SizedBox(width: 16),
                  ],
                  
                  // مدة العمل
                  Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    workHistory.durationText,
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // الصف الثالث: التواريخ
              Row(
                children: [
                  Icon(Icons.date_range, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${_formatDate(workHistory.startDate)} - ${workHistory.endDate != null ? _formatDate(workHistory.endDate!) : 'مستمر'}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),
              
              // الملاحظات إذا وجدت
              if (workHistory.notes?.isNotEmpty == true) ...[
                const SizedBox(height: 8),
                Text(
                  workHistory.notes!,
                  style: TextStyle(
                    color: Colors.grey[700],
                    fontSize: 12,
                    fontStyle: FontStyle.italic,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          const Text(
            'حدث خطأ في تحميل البيانات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            controller.error.value,
            style: TextStyle(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => controller.loadWorkHistory(refresh: true),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.work_history, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            controller.message.value.isNotEmpty
                ? 'لا توجد نتائج'
                : 'لا توجد سجلات عمل',
            style: const TextStyle(fontSize: 18, color: Colors.grey),
          ),
          const SizedBox(height: 8),
          Text(
            controller.message.value.isNotEmpty
                ? controller.message.value
                : 'اضغط على زر + لإضافة سجل عمل جديد',
            style: const TextStyle(color: Colors.grey),
            textAlign: TextAlign.center,
          ),

          // رسالة إضافية إذا لم يكن هناك موظف محدد
          if (controller.message.value.isNotEmpty && controller.selectedEmployeeId.value == null)
            const Padding(
              padding: EdgeInsets.only(top: 8),
              child: Text(
                'يمكنك اختيار موظف محدد لعرض تاريخ عمله، أو إضافة سجل عمل جديد',
                style: TextStyle(color: Colors.grey, fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ),
          const SizedBox(height: 16),

          // إظهار أزرار مختلفة حسب الحالة
          if (controller.message.value.isNotEmpty) ...[
            // إذا كانت هناك رسالة (مثل "يرجى تحديد موظف")
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _showEmployeeFilterDialog(),
                  icon: const Icon(Icons.person_search),
                  label: const Text('اختيار موظف'),
                ),
                const SizedBox(width: 16),
                OutlinedButton.icon(
                  onPressed: () => _navigateToAddWorkHistory(),
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة سجل جديد'),
                ),
              ],
            ),
          ] else ...[
            // الحالة العادية
            ElevatedButton.icon(
              onPressed: () => _navigateToAddWorkHistory(),
              icon: const Icon(Icons.add),
              label: const Text('إضافة سجل عمل جديد'),
            ),
          ],
        ],
      ),
    );
  }

  /// عرض حوار البحث
  void _showSearchDialog(BuildContext context) {
    final searchController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('البحث في تاريخ العمل'),
        content: TextField(
          controller: searchController,
          decoration: const InputDecoration(
            hintText: 'ادخل اسم الشركة أو المنصب أو اسم الموظف',
            prefixIcon: Icon(Icons.search),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              controller.searchWorkHistory(searchController.text);
              Get.back();
            },
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار اختيار الموظف
  void _showEmployeeFilterDialog() {
    // التأكد من تحميل الموظفين
    if (employeeController.employees.isEmpty) {
      employeeController.loadEmployees();
    }

    Get.dialog(
      AlertDialog(
        title: const Text('اختيار موظف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('يرجى اختيار موظف لعرض تاريخ عمله:'),
            const SizedBox(height: 16),
            Obx(() {
              return DropdownButtonFormField<int>(
                decoration: const InputDecoration(
                  labelText: 'الموظف',
                  hintText: 'اختر الموظف',
                  prefixIcon: Icon(Icons.person),
                ),
                value: null, // دائماً ابدأ بقيمة فارغة في حوار الاختيار
                items: employeeController.employees.isNotEmpty
                    ? employeeController.employees.map<DropdownMenuItem<int>>((employee) {
                        return DropdownMenuItem<int>(
                          value: employee.id,
                          child: Text(employee.fullNameArabic ?? employee.name),
                        );
                      }).toList()
                    : [
                        const DropdownMenuItem<int>(
                          value: -1,
                          enabled: false,
                          child: Text('جاري تحميل الموظفين...'),
                        ),
                      ],
                onChanged: employeeController.employees.isNotEmpty
                    ? (value) {
                        if (value != null && value != -1) {
                          controller.filterByEmployee(value);
                          Get.back();
                        }
                      }
                    : null,
              );
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار الفلترة
  void _showFilterDialog(BuildContext context) {
    // التأكد من تحميل الموظفين
    if (employeeController.employees.isEmpty) {
      employeeController.loadEmployees();
    }

    // التحقق من صحة الموظف المحدد
    controller.validateSelectedEmployee(employeeController.employees);

    Get.dialog(
      AlertDialog(
        title: const Text('فلترة تاريخ العمل'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400, // تحديد ارتفاع ثابت
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
              // فلتر الموظف
              Obx(() {
                // الحصول على قيمة صالحة للموظف
                final selectedEmployeeId = controller.selectedEmployeeId.value;
                final validEmployeeId = employeeController.employees.isNotEmpty && selectedEmployeeId != null
                    ? (employeeController.employees.any((emp) => emp.id == selectedEmployeeId) ? selectedEmployeeId : null)
                    : null;

                return DropdownButtonFormField<int>(
                  decoration: const InputDecoration(labelText: 'الموظف'),
                  value: validEmployeeId,
                  items: [
                    const DropdownMenuItem(value: null, child: Text('جميع الموظفين')),
                    if (employeeController.employees.isNotEmpty)
                      ...employeeController.employees.map(
                        (employee) => DropdownMenuItem(
                          value: employee.id,
                          child: Text(employee.fullNameArabic ?? employee.name),
                        ),
                      )
                    else
                      const DropdownMenuItem(
                        value: -1,
                        enabled: false,
                        child: Text('جاري تحميل الموظفين...'),
                      ),
                  ],
                  onChanged: employeeController.employees.isNotEmpty
                      ? (value) {
                          controller.filterByEmployee(value);
                          // إذا تم تغيير القيمة لموظف غير موجود، مسح الاختيار
                          if (value != null && !employeeController.employees.any((emp) => emp.id == value)) {
                            controller.filterByEmployee(null);
                          }
                        }
                      : null,
                );
              }),
              const SizedBox(height: 16),

              // فلتر نوع المنظمة
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(labelText: 'نوع المنظمة'),
                value: controller.selectedOrganizationType.value.isEmpty ? null : controller.selectedOrganizationType.value,
                items: [
                  const DropdownMenuItem(value: null, child: Text('جميع الأنواع')),
                  ...controller.getOrganizationTypes().map(
                    (type) => DropdownMenuItem(
                      value: type['value'],
                      child: Text(type['label']!),
                    ),
                  ),
                ],
                onChanged: (value) => controller.filterByOrganizationType(value ?? ''),
              ),
              const SizedBox(height: 16),

              // فلتر حالة العمل
              DropdownButtonFormField<bool?>(
                decoration: const InputDecoration(labelText: 'حالة العمل'),
                value: controller.selectedIsCurrent.value,
                items: const [
                  DropdownMenuItem(value: null, child: Text('جميع الحالات')),
                  DropdownMenuItem(value: true, child: Text('الوظائف الحالية')),
                  DropdownMenuItem(value: false, child: Text('الوظائف السابقة')),
                ],
                onChanged: (value) => controller.filterByCurrentStatus(value),
              ),
            ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          if (_hasActiveFilters())
            TextButton(
              onPressed: () {
                controller.clearFilters();
                Get.back();
              },
              child: const Text('مسح الفلاتر'),
            ),
          ElevatedButton(
            onPressed: () => Get.back(),
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  /// معالجة التصدير
  void _handleExport(String type) {
    switch (type) {
      case 'excel':
        _exportToExcel();
        break;
      case 'pdf':
        _exportToPdf();
        break;
    }
  }

  /// تصدير إلى Excel
  void _exportToExcel() {
    controller.exportWorkHistory(format: 'excel');
  }

  /// تصدير إلى PDF
  void _exportToPdf() {
    controller.exportWorkHistory(format: 'pdf');
  }

  /// التنقل لإضافة سجل عمل جديد
  void _navigateToAddWorkHistory() {
    debugPrint('🚀 محاولة التنقل إلى: /hr/work-history/add');
    Get.toNamed('/hr/work-history/add');
  }

  /// التنقل لتفاصيل سجل العمل
  void _navigateToWorkHistoryDetails(int id) {
    debugPrint('🚀 محاولة التنقل إلى تفاصيل سجل العمل: /hr/work-history/details/$id');
    Get.toNamed('/hr/work-history/details/$id');
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// حساب عدد الوظائف الحالية
  int _getCurrentJobsCount() {
    return controller.workHistoryList.where((wh) => wh.isCurrent).length;
  }

  /// حساب عدد الوظائف السابقة
  int _getPreviousJobsCount() {
    return controller.workHistoryList.where((wh) => !wh.isCurrent).length;
  }

  /// حساب متوسط مدة العمل
  String _getAverageDuration() {
    if (controller.workHistoryList.isEmpty) return '0';

    final totalMonths = controller.workHistoryList
        .map((wh) => wh.durationInMonths)
        .reduce((a, b) => a + b);

    final averageMonths = totalMonths / controller.workHistoryList.length;

    if (averageMonths < 12) {
      return '${averageMonths.round()} شهر';
    } else {
      final years = (averageMonths / 12).toStringAsFixed(1);
      return '$years سنة';
    }
  }

  /// التحقق من وجود فلاتر نشطة
  bool _hasActiveFilters() {
    return controller.searchTerm.value.isNotEmpty ||
           controller.selectedEmployeeId.value != null ||
           controller.selectedOrganizationType.value.isNotEmpty ||
           controller.selectedIsCurrent.value != null;
  }



  /// الحصول على نص معلومات الفلترة
  String _getFilterInfoText() {
    final totalCount = controller.totalCount.value;
    final hasFilters = _hasActiveFilters();

    if (hasFilters) {
      final activeFilters = <String>[];

      if (controller.searchTerm.value.isNotEmpty) {
        activeFilters.add('البحث: "${controller.searchTerm.value}"');
      }

      if (controller.selectedEmployeeId.value != null) {
        Employee? employee;
        if (employeeController.employees.isNotEmpty) {
          try {
            employee = employeeController.employees.firstWhere(
              (e) => e.id == controller.selectedEmployeeId.value
            );
          } catch (e) {
            employee = null;
          }
        }

        if (employee != null) {
          activeFilters.add('الموظف: ${employee.fullNameArabic ?? employee.name}');
        } else if (controller.selectedEmployeeId.value != null) {
          activeFilters.add('الموظف: ID ${controller.selectedEmployeeId.value}');
        }
      }

      if (controller.selectedOrganizationType.value.isNotEmpty) {
        final orgTypes = controller.getOrganizationTypes();
        Map<String, String>? orgType;
        try {
          orgType = orgTypes.firstWhere(
            (type) => type['value'] == controller.selectedOrganizationType.value
          );
        } catch (e) {
          orgType = null;
        }

        if (orgType != null) {
          activeFilters.add('نوع المنظمة: ${orgType['label']}');
        }
      }

      if (controller.selectedIsCurrent.value != null) {
        activeFilters.add(
          'الحالة: ${controller.selectedIsCurrent.value! ? 'الوظائف الحالية' : 'الوظائف السابقة'}'
        );
      }

      return 'تم العثور على $totalCount نتيجة • ${activeFilters.join(' • ')}';
    } else {
      if (totalCount == 0 && controller.selectedEmployeeId.value == null) {
        return 'لا يوجد موظف محدد - يرجى اختيار موظف لعرض تاريخ عمله';
      } else {
        return 'إجمالي السجلات: $totalCount';
      }
    }
  }

  @override
  void dispose() {
    _dataGridController.dispose();
    super.dispose();
  }
}
