import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/hr_controllers.dart';
import '../../models/hr_models.dart';
import '../../../../constants/app_colors.dart';
import '../../../../constants/app_styles.dart';
import '../../../../widgets/loading_indicator.dart';
import '../../../../helpers/user_helper.dart';

/// شاشة إضافة سجل عمل جديد
class AddWorkHistoryScreen extends StatefulWidget {
  const AddWorkHistoryScreen({super.key});

  @override
  State<AddWorkHistoryScreen> createState() => _AddWorkHistoryScreenState();
}

class _AddWorkHistoryScreenState extends State<AddWorkHistoryScreen> {
  late WorkHistoryController controller;
  late EmployeeController employeeController;
  final _formKey = GlobalKey<FormState>();

  // Controllers للحقول
  final _jobTitleController = TextEditingController();
  final _companyNameController = TextEditingController();
  final _leavingReasonController = TextEditingController();
  final _notesController = TextEditingController();

  // المتغيرات
  int? _selectedEmployeeId;
  String? _selectedOrganizationType;
  String? _selectedLeavingReason;
  DateTime _startDate = DateTime.now();
  DateTime? _endDate;
  DateTime? _transferDate;
  bool _isCurrent = false;
  bool _isLoading = false;

  // قائمة الموظفين
  final RxList<Map<String, dynamic>> employees = <Map<String, dynamic>>[].obs;

  // وضع التعديل
  bool _isEditMode = false;
  int? _editingWorkHistoryId;

  @override
  void initState() {
    super.initState();
    
    // تسجيل المتحكمات
    if (!Get.isRegistered<WorkHistoryController>()) {
      Get.lazyPut(() => WorkHistoryController());
    }
    if (!Get.isRegistered<EmployeeController>()) {
      Get.lazyPut(() => EmployeeController());
    }
    
    controller = Get.find<WorkHistoryController>();
    employeeController = Get.find<EmployeeController>();
    
    _loadEmployees();

    // التحقق من وضع التعديل
    final workHistoryId = Get.arguments as int?;
    if (workHistoryId != null) {
      _isEditMode = true;
      _loadWorkHistoryForEdit(workHistoryId);
    } else {
      // إذا كان المستخدم الحالي موظف، اختره تلقائياً
      final currentUser = UserHelper.getCurrentUser();
      if (currentUser != null) {
        _selectedEmployeeId = currentUser.id;
      }
    }
  }

  @override
  void dispose() {
    _jobTitleController.dispose();
    _companyNameController.dispose();
    _leavingReasonController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// تحميل قائمة الموظفين
  Future<void> _loadEmployees() async {
    try {
      await employeeController.loadEmployees();

      final uniqueEmployees = <int, Map<String, dynamic>>{};

      for (final employee in employeeController.employees) {
        uniqueEmployees[employee.id] = {
          'id': employee.id,
          'name': employee.fullNameArabic ?? employee.name,
          'department': employee.departmentName ?? 'غير محدد',
          'employeeId': employee.employeeId ?? '',
          'jobTitle': employee.jobTitle ?? '',
        };
      }

      employees.value = uniqueEmployees.values.toList();

      // التحقق من صحة الموظف المختار
      if (_selectedEmployeeId != null) {
        final employeeExists = employees.any((emp) => emp['id'] == _selectedEmployeeId);
        if (!employeeExists) {
          setState(() {
            _selectedEmployeeId = null;
          });
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الموظفين: $e');
      Get.snackbar('خطأ', 'فشل في تحميل قائمة الموظفين');
    }
  }

  /// تحميل بيانات سجل العمل للتعديل
  Future<void> _loadWorkHistoryForEdit(int workHistoryId) async {
    try {
      _editingWorkHistoryId = workHistoryId;
      await controller.getWorkHistoryDetails(workHistoryId);
      
      final workHistory = controller.selectedWorkHistory.value;
      if (workHistory != null) {
        setState(() {
          _selectedEmployeeId = workHistory.employeeId;
          _selectedOrganizationType = workHistory.organizationType;
          _jobTitleController.text = workHistory.jobTitle;
          _companyNameController.text = workHistory.companyName;
          _startDate = workHistory.startDate;
          _endDate = workHistory.endDate;
          _transferDate = workHistory.transferDate;
          _leavingReasonController.text = workHistory.leavingReason ?? '';
          _notesController.text = workHistory.notes ?? '';
          _isCurrent = workHistory.isCurrent;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات سجل العمل: $e');
      Get.snackbar('خطأ', 'فشل في تحميل بيانات سجل العمل');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditMode ? 'تعديل سجل العمل' : 'إضافة سجل عمل جديد'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _submitForm,
            child: Text(
              'حفظ',
              style: TextStyle(
                color: _isLoading ? Colors.grey : Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingIndicator()
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات أساسية
                    _buildSectionCard(
                      title: 'المعلومات الأساسية',
                      icon: Icons.info_outline,
                      children: [
                        // اختيار الموظف
                        _buildEmployeeDropdown(),
                        const SizedBox(height: 16),

                        // المنصب الوظيفي
                        _buildJobTitleField(),
                        const SizedBox(height: 16),

                        // اسم الشركة
                        _buildCompanyNameField(),
                        const SizedBox(height: 16),

                        // نوع المنظمة
                        _buildOrganizationTypeDropdown(),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // التواريخ
                    _buildSectionCard(
                      title: 'التواريخ',
                      icon: Icons.date_range,
                      children: [
                        // تاريخ البداية
                        _buildStartDatePicker(),
                        const SizedBox(height: 16),

                        // العمل الحالي
                        _buildCurrentJobSwitch(),
                        const SizedBox(height: 16),

                        // تاريخ الانتهاء (إذا لم يكن العمل الحالي)
                        if (!_isCurrent) ...[
                          _buildEndDatePicker(),
                          const SizedBox(height: 16),
                        ],

                        // تاريخ النقل (اختياري)
                        _buildTransferDatePicker(),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // معلومات إضافية
                    _buildSectionCard(
                      title: 'معلومات إضافية',
                      icon: Icons.description,
                      children: [
                        // سبب ترك العمل (إذا لم يكن العمل الحالي)
                        if (!_isCurrent) ...[
                          _buildLeavingReasonDropdown(),
                          const SizedBox(height: 16),
                        ],

                        // ملاحظات
                        _buildNotesField(),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // أزرار الإجراءات
                    _buildActionButtons(),

                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
    );
  }

  /// بناء بطاقة قسم
  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  /// بناء قائمة اختيار الموظف
  Widget _buildEmployeeDropdown() {
    return Obx(() {
      if (employees.isEmpty) {
        return Container(
          height: 60,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 10),
                Text('جاري تحميل الموظفين...'),
              ],
            ),
          ),
        );
      }

      final validEmployeeId = employees.any((emp) => emp['id'] == _selectedEmployeeId)
          ? _selectedEmployeeId
          : null;

      if (_selectedEmployeeId != null && validEmployeeId == null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          setState(() {
            _selectedEmployeeId = null;
          });
        });
      }

      return DropdownButtonFormField<int>(
        value: validEmployeeId,
        decoration: InputDecoration(
          labelText: 'الموظف *',
          hintText: 'اختر الموظف',
          prefixIcon: const Icon(Icons.person),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        items: employees.map<DropdownMenuItem<int>>((employee) {
          return DropdownMenuItem<int>(
            value: employee['id'] as int,
            child: Container(
              width: double.infinity,
              constraints: const BoxConstraints(maxWidth: 350, maxHeight: 40),
              padding: const EdgeInsets.symmetric(vertical: 1, horizontal: 4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 1),
                  Row(
                    children: [
                      if (employee['employeeId'] != null &&
                          employee['employeeId'].toString().isNotEmpty)
                        Flexible(
                          child: Text(
                            'رقم: ${employee['employeeId']}',
                            style: TextStyle(
                              fontSize: 9,
                              color: Colors.blue.shade600,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      const SizedBox(width: 8),
                      Flexible(
                        child: Text(
                          employee['name']?.toString() ?? 'غير محدد',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedEmployeeId = value;
          });
        },
        validator: (value) {
          if (value == null) {
            return 'يجب اختيار الموظف';
          }
          return null;
        },
      );
    });
  }

  /// بناء حقل المنصب الوظيفي
  Widget _buildJobTitleField() {
    return TextFormField(
      controller: _jobTitleController,
      decoration: InputDecoration(
        labelText: 'المنصب الوظيفي *',
        hintText: 'أدخل المنصب الوظيفي',
        prefixIcon: const Icon(Icons.work),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'المنصب الوظيفي مطلوب';
        }
        if (value.length > 100) {
          return 'المنصب الوظيفي طويل جداً';
        }
        return null;
      },
    );
  }

  /// بناء حقل اسم الشركة
  Widget _buildCompanyNameField() {
    return TextFormField(
      controller: _companyNameController,
      decoration: InputDecoration(
        labelText: 'اسم الشركة/المؤسسة *',
        hintText: 'أدخل اسم الشركة أو المؤسسة',
        prefixIcon: const Icon(Icons.business),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'اسم الشركة مطلوب';
        }
        if (value.length > 200) {
          return 'اسم الشركة طويل جداً';
        }
        return null;
      },
    );
  }

  /// بناء قائمة اختيار نوع المنظمة
  Widget _buildOrganizationTypeDropdown() {
    final organizationTypes = controller.getOrganizationTypes();

    return DropdownButtonFormField<String>(
      value: _selectedOrganizationType,
      decoration: InputDecoration(
        labelText: 'نوع المنظمة',
        hintText: 'اختر نوع المنظمة',
        prefixIcon: const Icon(Icons.category),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      items: organizationTypes.map((type) {
        return DropdownMenuItem<String>(
          value: type['value'],
          child: Text(type['label']!),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedOrganizationType = value;
        });
      },
    );
  }

  /// بناء منتقي تاريخ البداية
  Widget _buildStartDatePicker() {
    return TextFormField(
      readOnly: true,
      decoration: InputDecoration(
        labelText: 'تاريخ بداية العمل *',
        hintText: 'اختر تاريخ بداية العمل',
        prefixIcon: const Icon(Icons.calendar_today),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      controller: TextEditingController(
        text: '${_startDate.day}/${_startDate.month}/${_startDate.year}',
      ),
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: _startDate,
          firstDate: DateTime(1950),
          lastDate: DateTime.now(),
        );
        if (date != null) {
          setState(() {
            _startDate = date;
            // إذا كان تاريخ الانتهاء قبل تاريخ البداية، امسحه
            if (_endDate != null && _endDate!.isBefore(_startDate)) {
              _endDate = null;
            }
          });
        }
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'تاريخ بداية العمل مطلوب';
        }
        return null;
      },
    );
  }

  /// بناء مفتاح العمل الحالي
  Widget _buildCurrentJobSwitch() {
    return SwitchListTile(
      title: const Text('هذا هو العمل الحالي'),
      subtitle: const Text('فعل هذا الخيار إذا كان الموظف لا يزال يعمل في هذه الوظيفة'),
      value: _isCurrent,
      onChanged: (value) {
        setState(() {
          _isCurrent = value;
          if (_isCurrent) {
            _endDate = null;
            _selectedLeavingReason = null;
            _leavingReasonController.clear();
          }
        });
      },
      activeColor: AppColors.primary,
    );
  }

  /// بناء منتقي تاريخ الانتهاء
  Widget _buildEndDatePicker() {
    return TextFormField(
      readOnly: true,
      decoration: InputDecoration(
        labelText: 'تاريخ انتهاء العمل *',
        hintText: 'اختر تاريخ انتهاء العمل',
        prefixIcon: const Icon(Icons.event_busy),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      controller: TextEditingController(
        text: _endDate != null ? '${_endDate!.day}/${_endDate!.month}/${_endDate!.year}' : '',
      ),
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: _endDate ?? _startDate.add(const Duration(days: 30)),
          firstDate: _startDate,
          lastDate: DateTime.now(),
        );
        if (date != null) {
          setState(() {
            _endDate = date;
          });
        }
      },
      validator: (value) {
        if (!_isCurrent && (value == null || value.isEmpty)) {
          return 'تاريخ انتهاء العمل مطلوب للوظائف السابقة';
        }
        return null;
      },
    );
  }

  /// بناء منتقي تاريخ النقل
  Widget _buildTransferDatePicker() {
    return TextFormField(
      readOnly: true,
      decoration: InputDecoration(
        labelText: 'تاريخ النقل (اختياري)',
        hintText: 'اختر تاريخ النقل إن وجد',
        prefixIcon: const Icon(Icons.transfer_within_a_station),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      controller: TextEditingController(
        text: _transferDate != null ? '${_transferDate!.day}/${_transferDate!.month}/${_transferDate!.year}' : '',
      ),
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: _transferDate ?? _startDate,
          firstDate: _startDate,
          lastDate: _endDate ?? DateTime.now(),
        );
        if (date != null) {
          setState(() {
            _transferDate = date;
          });
        }
      },
    );
  }

  /// بناء قائمة اختيار سبب ترك العمل
  Widget _buildLeavingReasonDropdown() {
    final leavingReasons = controller.getLeavingReasons();

    return DropdownButtonFormField<String>(
      value: _selectedLeavingReason,
      decoration: InputDecoration(
        labelText: 'سبب ترك العمل',
        hintText: 'اختر سبب ترك العمل',
        prefixIcon: const Icon(Icons.exit_to_app),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      items: leavingReasons.map((reason) {
        return DropdownMenuItem<String>(
          value: reason['value'],
          child: Text(reason['label']!),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedLeavingReason = value;
          if (value == 'other') {
            // إذا اختار "أخرى"، اجعل حقل الملاحظات مطلوب
          } else {
            _leavingReasonController.text = value ?? '';
          }
        });
      },
    );
  }

  /// بناء حقل الملاحظات
  Widget _buildNotesField() {
    return TextFormField(
      controller: _notesController,
      decoration: InputDecoration(
        labelText: 'ملاحظات إضافية',
        hintText: 'أي ملاحظات أو تفاصيل إضافية',
        prefixIcon: const Icon(Icons.note),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      maxLines: 3,
      validator: (value) {
        if (value != null && value.length > 500) {
          return 'الملاحظات طويلة جداً';
        }
        return null;
      },
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Get.back(),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('إلغاء'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _submitForm,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(_isEditMode ? 'حفظ التعديلات' : 'حفظ السجل'),
          ),
        ),
      ],
    );
  }

  /// إرسال النموذج
  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isEditMode && _editingWorkHistoryId != null) {
        // تحديث سجل العمل
        final updateDto = UpdateWorkHistoryDto(
          jobTitle: _jobTitleController.text,
          companyName: _companyNameController.text,
          startDate: _startDate,
          endDate: _endDate,
          organizationType: _selectedOrganizationType,
          leavingReason: _selectedLeavingReason == 'other'
              ? _leavingReasonController.text
              : _selectedLeavingReason,
          transferDate: _transferDate,
          notes: _notesController.text.isNotEmpty ? _notesController.text : null,
          isCurrent: _isCurrent,
        );

        final success = await controller.updateWorkHistory(_editingWorkHistoryId!, updateDto);

        if (success) {
          Get.snackbar(
            '✅ نجح',
            'تم تحديث سجل العمل بنجاح',
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
          Get.back();
        }
      } else {
        // إنشاء سجل عمل جديد
        final createDto = CreateWorkHistoryDto(
          employeeId: _selectedEmployeeId!,
          jobTitle: _jobTitleController.text,
          companyName: _companyNameController.text,
          startDate: _startDate,
          endDate: _endDate,
          organizationType: _selectedOrganizationType,
          leavingReason: _selectedLeavingReason == 'other'
              ? _leavingReasonController.text
              : _selectedLeavingReason,
          transferDate: _transferDate,
          notes: _notesController.text.isNotEmpty ? _notesController.text : null,
          isCurrent: _isCurrent,
        );

        final success = await controller.createWorkHistory(createDto);

        if (success) {
          Get.snackbar(
            '✅ نجح',
            'تم إضافة سجل العمل بنجاح',
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
          Get.back();
        }
      }
    } catch (e) {
      debugPrint('خطأ في حفظ سجل العمل: $e');
      Get.snackbar(
        'خطأ',
        _isEditMode
            ? 'حدث خطأ أثناء تحديث سجل العمل. يرجى المحاولة مرة أخرى.'
            : 'حدث خطأ أثناء إضافة سجل العمل. يرجى المحاولة مرة أخرى.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
