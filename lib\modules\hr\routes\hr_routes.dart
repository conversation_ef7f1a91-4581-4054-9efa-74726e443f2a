import 'package:get/get.dart';
import '../bindings/hr_bindings.dart';
import '../screens/hr_screens.dart';

/// مسارات الموارد البشرية
class HRRoutes {
  // Base HR route
  static const String hr = '/hr';
  
  // Employee routes
  static const String employees = '/hr/employees';
  static const String employeeDetails = '/hr/employees/:id';
  static const String addEmployee = '/hr/employees/add';
  static const String editEmployee = '/hr/employees/:id/edit';
  static const String employeeProfile = '/hr/employees/:id/profile';
  
  // Attendance routes
  static const String attendance = '/hr/attendance';
  static const String employeeAttendance = '/hr/attendance/employee/:id';
  static const String checkIn = '/hr/attendance/check-in';
  static const String checkOut = '/hr/attendance/check-out';
  static const String attendanceReport = '/hr/attendance/report';
  
  // Payroll routes
  static const String payroll = '/hr/payroll';
  static const String employeePayroll = '/hr/payroll/employee/:id';
  static const String addPayroll = '/hr/payroll/add';
  static const String editPayroll = '/hr/payroll/:id/edit';
  static const String payrollReport = '/hr/payroll/report';
  
  // Leave routes
  static const String leaves = '/hr/leaves';
  static const String employeeLeaves = '/hr/leaves/employee/:id';
  static const String addLeave = '/hr/leaves/add';
  static const String editLeave = '/hr/leaves/:id/edit';


  static const String leaveApproval = '/hr/leaves/approval';
  static const String leaveBalance = '/hr/leaves/balance/:employeeId';
  
  // Work History routes
  static const String workHistory = '/hr/work-history';
  static const String employeeWorkHistory = '/hr/work-history/employee/:id';
  static const String addWorkHistory = '/hr/work-history/add';
  static const String workHistoryDetails = '/hr/work-history/details/:id';
  static const String editWorkHistory = '/hr/work-history/:id/edit';
  
  // Training routes
  static const String training = '/hr/training';
  static const String employeeTraining = '/hr/training/employee/:id';
  static const String addTraining = '/hr/training/add';
  static const String editTraining = '/hr/training/:id/edit';
  static const String trainingReport = '/hr/training/report';
  
  // Performance routes
  static const String performance = '/hr/performance';
  static const String employeePerformance = '/hr/performance/employee/:id';
  static const String addPerformance = '/hr/performance/add';
  static const String editPerformance = '/hr/performance/:id/edit';
  static const String performanceReport = '/hr/performance/report';
  
  // Asset routes
  static const String assets = '/hr/assets';
  static const String employeeAssets = '/hr/assets/employee/:id';
  static const String addAsset = '/hr/assets/add';
  static const String editAsset = '/hr/assets/:id/edit';
  static const String assetReturn = '/hr/assets/:id/return';
  
  // Advance routes
  static const String advances = '/hr/advances';
  static const String employeeAdvances = '/hr/advances/employee/:id';
  static const String addAdvance = '/hr/advances/add';
  static const String editAdvance = '/hr/advances/:id/edit';
  static const String advanceDetails = '/hr/advances/:id/details';
  static const String advanceApproval = '/hr/advances/approval';
  
  // HR Document routes
  static const String documents = '/hr/documents';
  static const String addDocument = '/hr/documents/add';
  static const String editDocument = '/hr/documents/:id/edit';
  static const String documentDetails = '/hr/documents/:id';
  
  // Report routes
  static const String reports = '/hr/reports';
  static const String employeeReport = '/hr/reports/employees';
  static const String attendanceReportPage = '/hr/reports/attendance';
  static const String payrollReportPage = '/hr/reports/payroll';
  static const String performanceReportPage = '/hr/reports/performance';
  
  // Dashboard routes
  static const String dashboard = '/hr/dashboard';
  static const String statistics = '/hr/statistics';
}

/// صفحات الموارد البشرية
class HRPages {
  static List<GetPage> pages = [
    // Main HR Dashboard
    GetPage(
      name: HRRoutes.hr,
      page: () => const HRDashboardScreen(),
      binding: HRBindings(),
    ),

    // Employee pages
    GetPage(
      name: HRRoutes.employees,
      page: () => const EmployeeListScreen(),
      binding: EmployeeBindings(),
    ),
    GetPage(
      name: HRRoutes.employeeDetails,
      page: () => const EmployeeDetailsScreen(),
      binding: EmployeeBindings(),
    ),
    GetPage(
      name: HRRoutes.addEmployee,
      page: () => const AddEmployeeScreen(),
      binding: EmployeeBindings(),
    ),
    GetPage(
      name: HRRoutes.editEmployee,
      page: () => const EditEmployeeScreen(),
      binding: EmployeeBindings(),
    ),

    // Attendance pages
    GetPage(
      name: HRRoutes.attendance,
      page: () => const AttendanceScreen(),
      binding: AttendanceBindings(),
    ),
    GetPage(
      name: HRRoutes.employeeAttendance,
      page: () => const EmployeeAttendanceScreen(),
      binding: AttendanceBindings(),
    ),
    GetPage(
      name: HRRoutes.checkIn,
      page: () => const CheckInScreen(),
      binding: AttendanceBindings(),
    ),

    // Payroll pages
    GetPage(
      name: HRRoutes.payroll,
      page: () => const PayrollScreen(),
      binding: PayrollBindings(),
    ),
    GetPage(
      name: HRRoutes.addPayroll,
      page: () => const AddPayrollScreen(),
      binding: PayrollBindings(),
    ),
    GetPage(
      name: HRRoutes.editPayroll,
      page: () => const EditPayrollScreen(),
      binding: PayrollBindings(),
    ),

    // Leave pages
    GetPage(
      name: HRRoutes.leaves,
      page: () => const LeavesScreen(),
      binding: LeaveBindings(),
    ),
    GetPage(
      name: HRRoutes.addLeave,
      page: () => const AddLeaveScreen(),
      binding: LeaveBindings(),
    ),
    GetPage(
      name: HRRoutes.editLeave,
      page: () => const EditLeaveScreen(),
      binding: LeaveBindings(),
    ),

    // Reports pages
    GetPage(
      name: HRRoutes.reports,
      page: () => const HRReportsScreen(),
      binding: HRBindings(),
    ),

    // Performance pages
    GetPage(
      name: HRRoutes.performance,
      page: () => const PerformanceScreen(),
      binding: HRBindings(),
    ),



    // Attendance pages
    GetPage(
      name: HRRoutes.checkIn,
      page: () => const CheckInScreen(),
      binding: HRBindings(),
    ),


    // Work History pages
    GetPage(
      name: HRRoutes.workHistory,
      page: () => const WorkHistoryScreen(),
      binding: HRBindings(),
    ),
    GetPage(
      name: HRRoutes.addWorkHistory,
      page: () => const AddWorkHistoryScreen(),
      binding: HRBindings(),
    ),
    GetPage(
      name: HRRoutes.workHistoryDetails,
      page: () => const WorkHistoryDetailsScreen(),
      binding: HRBindings(),
    ),
    GetPage(
      name: HRRoutes.editWorkHistory,
      page: () => const AddWorkHistoryScreen(), // نفس شاشة الإضافة ولكن في وضع التعديل
      binding: HRBindings(),
    ),

    // Training pages
    GetPage(
      name: HRRoutes.training,
      page: () => const TrainingScreen(),
      binding: HRBindings(),
    ),

    // Asset pages
    GetPage(
      name: HRRoutes.assets,
      page: () => const AssetScreen(),
      binding: HRBindings(),
    ),

    // Advance pages
    GetPage(
      name: HRRoutes.advances,
      page: () => const AdvanceScreen(),
      binding: HRBindings(),
    ),
    GetPage(
      name: HRRoutes.addAdvance,
      page: () => const AddAdvanceScreen(),
      binding: HRBindings(),
    ),
    GetPage(
      name: HRRoutes.advanceDetails,
      page: () => const AdvanceDetailsScreen(),
      binding: HRBindings(),
    ),
    GetPage(
      name: HRRoutes.editAdvance,
      page: () => const AddAdvanceScreen(), // نفس شاشة الإضافة ولكن في وضع التعديل
      binding: HRBindings(),
    ),

    // HR Document pages
    GetPage(
      name: HRRoutes.documents,
      page: () => const HRDocumentScreen(),
      binding: HRBindings(),
    ),
  ];
}

/// مساعد للتنقل في الموارد البشرية
class HRNavigator {
  /// الانتقال إلى لوحة تحكم الموارد البشرية
  static void toHRDashboard() {
    Get.toNamed(HRRoutes.hr);
  }
  
  /// الانتقال إلى قائمة الموظفين
  static void toEmployeeList() {
    Get.toNamed(HRRoutes.employees);
  }
  
  /// الانتقال إلى تفاصيل الموظف
  static void toEmployeeDetails(int employeeId) {
    Get.toNamed(HRRoutes.employeeDetails.replaceAll(':id', employeeId.toString()));
  }
  
  /// الانتقال إلى إضافة موظف جديد
  static void toAddEmployee() {
    Get.toNamed(HRRoutes.addEmployee);
  }
  
  /// الانتقال إلى تعديل الموظف
  static void toEditEmployee(int employeeId) {
    Get.toNamed(HRRoutes.editEmployee.replaceAll(':id', employeeId.toString()));
  }
  
  /// الانتقال إلى الحضور والانصراف
  static void toAttendance() {
    Get.toNamed(HRRoutes.attendance);
  }
  
  /// الانتقال إلى حضور الموظف
  static void toEmployeeAttendance(int employeeId) {
    Get.toNamed(HRRoutes.employeeAttendance.replaceAll(':id', employeeId.toString()));
  }
  
  /// الانتقال إلى الرواتب
  static void toPayroll() {
    Get.toNamed(HRRoutes.payroll);
  }

  /// الانتقال إلى إضافة كشف راتب جديد
  static void toAddPayroll() {
    Get.toNamed(HRRoutes.addPayroll);
  }

  /// الانتقال إلى تعديل كشف راتب
  static void toEditPayroll(int payrollId) {
    Get.toNamed(HRRoutes.editPayroll.replaceAll(':id', payrollId.toString()));
  }

  /// الانتقال إلى تفاصيل كشف راتب
  static void toPayrollDetails(int payrollId) {
    Get.toNamed('/hr/payroll/$payrollId/details');
  }

  /// الانتقال إلى رواتب الموظف
  static void toEmployeePayroll(int employeeId) {
    Get.toNamed('/hr/employees/$employeeId/payroll');
  }

  /// الانتقال إلى الإجازات
  static void toLeaves() {
    Get.toNamed(HRRoutes.leaves);
  }

  /// الانتقال إلى إجازات الموظف
  static void toEmployeeLeaves(int employeeId) {
    Get.toNamed(HRRoutes.employeeLeaves.replaceAll(':id', employeeId.toString()));
  }

  /// الانتقال إلى إضافة إجازة جديدة
  static void toAddLeave() {
    Get.toNamed(HRRoutes.addLeave);
  }

  /// الانتقال إلى تفاصيل الإجازة
  static void toLeaveDetails(int leaveId) {
    Get.toNamed('/hr/leaves/$leaveId/details');
  }
  
  /// الانتقال إلى تاريخ العمل
  static void toWorkHistory() {
    Get.toNamed(HRRoutes.workHistory);
  }

  /// الانتقال إلى إضافة سجل عمل جديد
  static void toAddWorkHistory() {
    Get.toNamed(HRRoutes.addWorkHistory);
  }

  /// الانتقال إلى تفاصيل سجل العمل
  static void toWorkHistoryDetails(int workHistoryId) {
    Get.toNamed(HRRoutes.workHistoryDetails.replaceAll(':id', workHistoryId.toString()));
  }

  /// الانتقال إلى تعديل سجل العمل
  static void toEditWorkHistory(int workHistoryId) {
    Get.toNamed(HRRoutes.editWorkHistory.replaceAll(':id', workHistoryId.toString()));
  }

  /// الانتقال إلى تاريخ عمل الموظف
  static void toEmployeeWorkHistory(int employeeId) {
    Get.toNamed(HRRoutes.employeeWorkHistory.replaceAll(':id', employeeId.toString()));
  }
  
  /// الانتقال إلى التدريب
  static void toTraining() {
    Get.toNamed(HRRoutes.training);
  }
  
  /// الانتقال إلى تقييم الأداء
  static void toPerformance() {
    Get.toNamed(HRRoutes.performance);
  }

  /// الانتقال إلى أداء الموظف
  static void toEmployeePerformance(int employeeId) {
    Get.toNamed('/hr/employees/$employeeId/performance');
  }
  
  /// الانتقال إلى العهد
  static void toAssets() {
    Get.toNamed(HRRoutes.assets);
  }
  
  /// الانتقال إلى السلف
  static void toAdvances() {
    Get.toNamed(HRRoutes.advances);
  }

  /// الانتقال إلى إضافة سلفة جديدة
  static void toAddAdvance() {
    Get.toNamed(HRRoutes.addAdvance);
  }

  /// الانتقال إلى تفاصيل السلفة
  static void toAdvanceDetails(int advanceId) {
    Get.toNamed(HRRoutes.advanceDetails.replaceAll(':id', advanceId.toString()));
  }

  /// الانتقال إلى تعديل السلفة
  static void toEditAdvance(int advanceId) {
    Get.toNamed(HRRoutes.editAdvance.replaceAll(':id', advanceId.toString()));
  }

  /// الانتقال إلى سلف الموظف
  static void toEmployeeAdvances(int employeeId) {
    Get.toNamed(HRRoutes.employeeAdvances.replaceAll(':id', employeeId.toString()));
  }

  /// الانتقال إلى المراسلات
  static void toDocuments() {
    Get.toNamed(HRRoutes.documents);
  }
  
  /// الانتقال إلى التقارير
  static void toReports() {
    Get.toNamed(HRRoutes.reports);
  }
  
  /// العودة للخلف
  static void back() {
    Get.back();
  }
  
  /// العودة إلى الشاشة الرئيسية
  static void toHome() {
    Get.offAllNamed('/');
  }
}
