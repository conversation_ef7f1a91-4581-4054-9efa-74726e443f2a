import 'package:get/get.dart';
import '../controllers/hr_controllers.dart';
import '../../../controllers/department_controller.dart';
import '../../../controllers/admin_controller.dart';
import '../services/hr_api_services.dart';

/// ربط تبعيات الموارد البشرية
class HRBindings extends Bindings {
  @override
  void dependencies() {
    // API Services
    Get.lazyPut<EmployeeApiService>(() => EmployeeApiService());
    Get.lazyPut<AttendanceApiService>(() => AttendanceApiService());
    Get.lazyPut<PayrollApiService>(() => PayrollApiService());
    Get.lazyPut<LeaveApiService>(() => LeaveApiService());
    Get.lazyPut<AdvanceApiService>(() => AdvanceApiService());
    Get.lazyPut<WorkHistoryApiService>(() => WorkHistoryApiService());

    // Employee Controller
    Get.lazyPut<EmployeeController>(() => EmployeeController());

    // Attendance Controller
    Get.lazyPut<AttendanceController>(() => AttendanceController());

    // Payroll Controller
    Get.lazyPut<PayrollController>(() => PayrollController());

    // Leave Controller
    Get.lazyPut<LeaveController>(() => LeaveController());

    // Work History Controller - تم تفعيله
    Get.lazyPut<WorkHistoryController>(() => WorkHistoryController());
    // Get.lazyPut<TrainingController>(() => TrainingController());
    // Get.lazyPut<PerformanceController>(() => PerformanceController());
    // Get.lazyPut<AssetController>(() => AssetController());
    Get.lazyPut<AdvanceController>(() => AdvanceController());
    // Get.lazyPut<HRDocumentController>(() => HRDocumentController());
    // Get.lazyPut<HRReportController>(() => HRReportController());
  }
}

/// ربط تبعيات الموظفين فقط
class EmployeeBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<EmployeeController>(() => EmployeeController());

    // إضافة Controllers المطلوبة لشاشة إضافة الموظف
    Get.lazyPut<DepartmentController>(() => DepartmentController());
    Get.lazyPut<AdminController>(() => AdminController());
  }
}

/// ربط تبعيات الحضور فقط
class AttendanceBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<AttendanceController>(() => AttendanceController());
  }
}

/// ربط تبعيات الرواتب فقط
class PayrollBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<PayrollController>(() => PayrollController());
  }
}

/// ربط تبعيات الإجازات فقط
class LeaveBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<LeaveController>(() => LeaveController());
    Get.lazyPut<LeaveApiService>(() => LeaveApiService());
  }
}

/// ربط تبعيات تاريخ العمل فقط
class WorkHistoryBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<WorkHistoryApiService>(() => WorkHistoryApiService());
    Get.lazyPut<WorkHistoryController>(() => WorkHistoryController());
    Get.lazyPut<EmployeeController>(() => EmployeeController());
  }
}

/// ربط تبعيات التدريب فقط
class TrainingBindings extends Bindings {
  @override
  void dependencies() {
    // TODO: إضافة عند إنشاء TrainingController
    // Get.lazyPut<TrainingController>(() => TrainingController());
  }
}

/// ربط تبعيات تقييم الأداء فقط
class PerformanceBindings extends Bindings {
  @override
  void dependencies() {
    // TODO: إضافة عند إنشاء PerformanceController
    // Get.lazyPut<PerformanceController>(() => PerformanceController());
  }
}

/// ربط تبعيات العهد فقط
class AssetBindings extends Bindings {
  @override
  void dependencies() {
    // TODO: إضافة عند إنشاء AssetController
    // Get.lazyPut<AssetController>(() => AssetController());
  }
}

/// ربط تبعيات السلف فقط
class AdvanceBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<AdvanceApiService>(() => AdvanceApiService());
    Get.lazyPut<AdvanceController>(() => AdvanceController());
  }
}

/// ربط تبعيات المراسلات فقط
class HRDocumentBindings extends Bindings {
  @override
  void dependencies() {
    // TODO: إضافة عند إنشاء HRDocumentController
    // Get.lazyPut<HRDocumentController>(() => HRDocumentController());
  }
}

/// ربط تبعيات التقارير فقط
class HRReportBindings extends Bindings {
  @override
  void dependencies() {
    // TODO: إضافة عند إنشاء HRReportController
    // Get.lazyPut<HRReportController>(() => HRReportController());
  }
}
