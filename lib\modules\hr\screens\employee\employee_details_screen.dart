import 'package:flutter/material.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:get/get.dart';
import '../../controllers/hr_controllers.dart';
import '../../models/hr_models.dart';
import '../../routes/hr_routes.dart';
// إضافات لعرض قسم مهام الموظف وتاريخ العمل والسلف والإجازات
import '../../../../services/api/task_api_service.dart';
import '../../../../models/task_model.dart';
import '../../../../routes/app_routes.dart';
import '../../services/work_history_api_service.dart';
import '../../services/advance_api_service.dart';
import '../../services/leave_api_service.dart';

/// شاشة تفاصيل الموظف
class EmployeeDetailsScreen extends StatelessWidget {
  const EmployeeDetailsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<EmployeeController>();
    final employeeId = int.parse(Get.parameters['id'] ?? '0');

    // تحميل تفاصيل الموظف عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.getEmployeeDetails(employeeId);
    });

    return Scaffold(
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.error.value.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                const SizedBox(height: 16),
                Text(
                  controller.error.value,
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.red),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => controller.getEmployeeDetails(employeeId),
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        final employee = controller.selectedEmployee.value;
        if (employee == null) {
          return const Center(
            child: Text('لم يتم العثور على الموظف'),
          );
        }

        return CustomScrollView(
          slivers: [
            _buildAppBar(context, employee, controller),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPersonalInfoSection(employee),
                    const SizedBox(height: 16),
                    _buildJobInfoSection(employee),
                    const SizedBox(height: 16),
                    _buildContactInfoSection(employee),
                    const SizedBox(height: 16),
                    _buildAddressInfoSection(employee),
                    const SizedBox(height: 16),
                    // قسم أعمال/مهام الموظف
                    _buildEmployeeTasksSection(context, employee),
                    const SizedBox(height: 16),
                    // قسم تاريخ العمل الخاص بالموظف
                    _buildEmployeeWorkHistorySection(context, employee),
                    const SizedBox(height: 16),
                    // قسم السلف والمساعدات المالية
                    _buildEmployeeAdvancesSection(context, employee),
                    const SizedBox(height: 16),
                    // قسم الإجازات
                    _buildEmployeeLeavesSection(context, employee),
                    const SizedBox(height: 16),
                    _buildQuickActionsSection(context, employee),
                  ],
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  /// بناء شريط التطبيق المخصص
  Widget _buildAppBar(BuildContext context, Employee employee, EmployeeController controller) {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      actions: [
        IconButton(
          icon: const Icon(Icons.edit),
          onPressed: () => HRNavigator.toEditEmployee(employee.id),
        ),
        PopupMenuButton<String>(
          onSelected: (action) => _handleMenuAction(context, action, employee, controller),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'change_status',
              child: Row(
                children: [
                  Icon(Icons.swap_horiz, size: 16),
                  SizedBox(width: 8),
                  Text('تغيير حالة التوظيف'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'view_attendance',
              child: Row(
                children: [
                  Icon(Icons.access_time, size: 16),
                  SizedBox(width: 8),
                  Text('عرض الحضور'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'view_payroll',
              child: Row(
                children: [
                  Icon(Icons.payment, size: 16),
                  SizedBox(width: 8),
                  Text('عرض الرواتب'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'view_leaves',
              child: Row(
                children: [
                  Icon(Icons.event_busy, size: 16),
                  SizedBox(width: 8),
                  Text('عرض الإجازات'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 16, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف الموظف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 60), // مساحة لشريط التطبيق
                CircleAvatar(
                  radius: 40,
                  backgroundColor: Colors.white,
                  backgroundImage: employee.profileImage?.isNotEmpty == true
                      ? NetworkImage(employee.profileImage!)
                      : null,
                  child: employee.profileImage?.isEmpty != false
                      ? Text(
                          _getInitials(employee.displayName),
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).primaryColor,
                          ),
                        )
                      : null,
                ),
                const SizedBox(height: 12),
                Text(
                  employee.displayName,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                if (employee.jobTitle?.isNotEmpty == true)
                  Text(
                    employee.jobTitle!,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء قسم المعلومات الشخصية
  Widget _buildPersonalInfoSection(Employee employee) {
    return _buildSection(
      title: 'المعلومات الشخصية',
      icon: Icons.person,
      children: [
        _buildInfoTile('الاسم الكامل بالعربية', employee.fullNameArabic),
        _buildInfoTile('الاسم بالإنجليزية', employee.name),
        _buildInfoTile('تاريخ الميلاد', employee.birthDate != null ? _formatDate(employee.birthDate!) : null),
        _buildInfoTile('مكان الميلاد', employee.birthPlace),
        _buildInfoTile('فصيلة الدم', employee.bloodType),
        _buildInfoTile('المؤهل العلمي', employee.qualification),
        if (employee.age != null)
          _buildInfoTile('العمر', '${employee.age} سنة'),
      ],
    );
  }

  /// بناء قسم المعلومات الوظيفية
  Widget _buildJobInfoSection(Employee employee) {
    return _buildSection(
      title: 'المعلومات الوظيفية',
      icon: Icons.work,
      children: [
        _buildInfoTile('رقم الموظف', employee.employeeId),
        _buildInfoTile('المنصب', employee.jobTitle),
        _buildInfoTile('القسم', employee.departmentName),
        _buildInfoTile('الدور', employee.roleName),
        _buildInfoTile('تاريخ التوظيف', employee.hireDate != null ? _formatDate(employee.hireDate!) : null),
        _buildInfoTile('حالة التوظيف', employee.employmentStatusArabic),
        _buildInfoTile('الدرجة الوظيفية', employee.salaryGrade),
        if (employee.yearsOfExperience != null)
          _buildInfoTile('سنوات الخبرة', '${employee.yearsOfExperience} سنة'),
      ],
    );
  }

  /// بناء قسم معلومات الاتصال
  Widget _buildContactInfoSection(Employee employee) {
    return _buildSection(
      title: 'معلومات الاتصال',
      icon: Icons.contact_phone,
      children: [
        _buildInfoTile('البريد الإلكتروني', employee.email),
        _buildInfoTile('اسم المستخدم', employee.username),
      ],
    );
  }

  /// بناء قسم معلومات العنوان
  Widget _buildAddressInfoSection(Employee employee) {
    return _buildSection(
      title: 'معلومات السكن',
      icon: Icons.location_on,
      children: [
        _buildInfoTile('العنوان الحالي', employee.currentAddress),
        _buildInfoTile('المحافظة', employee.governorate),
        _buildInfoTile('المديرية', employee.directorate),
        _buildInfoTile('العزلة', employee.isolation),
        _buildInfoTile('القرية', employee.village),
      ],
    );
  }

  /// بناء قسم الإجراءات السريعة
  Widget _buildQuickActionsSection(BuildContext context, Employee employee) {
    return _buildSection(
      title: 'الإجراءات السريعة',
      icon: Icons.flash_on,
      children: [
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => HRNavigator.toEmployeeAttendance(employee.id),
                icon: const Icon(Icons.access_time),
                label: const Text('الحضور'),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => HRNavigator.toEmployeePayroll(employee.id),
                icon: const Icon(Icons.payment),
                label: const Text('الرواتب'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => HRNavigator.toEmployeeLeaves(employee.id),
                icon: const Icon(Icons.event_busy),
                label: const Text('الإجازات'),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => HRNavigator.toEmployeePerformance(employee.id),
                icon: const Icon(Icons.trending_up),
                label: const Text('الأداء'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء قسم
  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const Divider(),
            ...children,
          ],
        ),
      ),
    );
  }

  /// بناء عنصر معلومة
  Widget _buildInfoTile(String label, String? value) {
    if (value?.isEmpty != false) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value!,
              style: const TextStyle(fontWeight: FontWeight.w400),
            ),
          ),
        ],
      ),
    );
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(
    BuildContext context,
    String action,
    Employee employee,
    EmployeeController controller,
  ) {
    switch (action) {
      case 'change_status':
        _showChangeStatusDialog(context, employee, controller);
        break;
      case 'view_attendance':
        HRNavigator.toEmployeeAttendance(employee.id);
        break;
      case 'view_payroll':
        HRNavigator.toEmployeePayroll(employee.id);
        break;
      case 'view_leaves':
        HRNavigator.toEmployeeLeaves(employee.id);
        break;
      case 'delete':
        _showDeleteConfirmation(context, employee, controller);
        break;
    }
  }

  /// عرض حوار تغيير حالة التوظيف
  void _showChangeStatusDialog(
    BuildContext context,
    Employee employee,
    EmployeeController controller,
  ) {
    String selectedStatus = employee.employmentStatus ?? 'active';
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير حالة التوظيف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<String>(
              value: selectedStatus,
              decoration: const InputDecoration(labelText: 'الحالة الجديدة'),
              items: const [
                DropdownMenuItem(value: 'active', child: Text('نشط')),
                DropdownMenuItem(value: 'inactive', child: Text('غير نشط')),
                DropdownMenuItem(value: 'terminated', child: Text('منتهي الخدمة')),
              ],
              onChanged: (value) => selectedStatus = value ?? 'active',
            ),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'سبب التغيير',
                hintText: 'اختياري...',
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final dto = ChangeEmploymentStatusDto(
                employmentStatus: selectedStatus,
                reason: reasonController.text.isEmpty ? null : reasonController.text,
                effectiveDate: DateTime.now(),
              );
              controller.changeEmploymentStatus(employee.id, dto);
              Get.back();
            },
            child: const Text('تغيير'),
          ),
        ],
      ),
    );
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation(
    BuildContext context,
    Employee employee,
    EmployeeController controller,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الموظف "${employee.displayName}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              controller.deleteEmployee(employee.id);
              Get.back();
              Get.back(); // العودة للقائمة
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  /// الحصول على الأحرف الأولى من الاسم
  String _getInitials(String name) {
    if (name.isEmpty) return '؟';

    final words = name.split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}';
    }
    return name[0];
  }

  /// قسم أعمال/مهام الموظف
  Widget _buildEmployeeTasksSection(BuildContext context, Employee employee) {

    return _buildSection(
      title: 'الأعمال والمهام الخاصة بالموظف',
      icon: Icons.task,
      children: [
        FutureBuilder<List<Task>>(
          future: _loadEmployeeTasks(employee.id),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Padding(
                padding: EdgeInsets.all(8.0),
                child: LinearProgressIndicator(),
              );
            }
            if (snapshot.hasError) {
              return Text('حدث خطأ أثناء تحميل المهام: ${snapshot.error}',
                  style: const TextStyle(color: Colors.red));
            }
            final tasks = snapshot.data ?? [];
            if (tasks.isEmpty) {
              return const Text('لا توجد مهام مسندة لهذا الموظف');
            }

            // قائمة مهام مبسطة
            return ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: tasks.length.clamp(0, 5), // عرض 5 مهام كحد أقصى هنا
              separatorBuilder: (_, __) => const Divider(height: 12),
              itemBuilder: (context, index) {
                final t = tasks[index];
                final statusColor = AppColors.getTaskStatusColor(t.status);
                return ListTile(
                  contentPadding: EdgeInsets.zero,
                  title:
                      Text(t.title, maxLines: 1, overflow: TextOverflow.ellipsis),
                  subtitle: Row(
                    children: [
                      Icon(Icons.flag,
                          size: 14,
                          color: AppColors.getTaskPriorityColor(t.priority)),
                      const SizedBox(width: 6),
                      Text('الحالة: ${t.status} • الإنجاز: ${t.completionPercentage}%'),
                    ],
                  ),
                  leading: CircleAvatar(
                    radius: 14,
                    backgroundColor: statusColor.withValues(alpha: 0.15),
                    child: Icon(Icons.checklist, color: statusColor, size: 16),
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.open_in_new),
                    tooltip: 'فتح التفاصيل',
                    onPressed: () {
                      Get.toNamed(AppRoutes.taskDetail,
                          arguments: {'taskId': t.id});
                    },
                  ),
                );
              },
            );
          },
        ),
        const SizedBox(height: 8),
        Align(
          alignment: Alignment.centerLeft,
          child: TextButton.icon(
            onPressed: () {
              // انتقال إلى شاشة المهام العامة مع فلتر المكلف = هذا الموظف
              Get.toNamed(AppRoutes.tasks, arguments: {'assigneeId': employee.id});
            },
            icon: const Icon(Icons.list),
            label: const Text('عرض جميع مهام الموظف'),
          ),
        ),
      ],
    );
  }

  Future<List<Task>> _loadEmployeeTasks(int employeeId) async {
    try {
      final api = TaskApiService();
      // تفضيل الاعتماد على جدول task_access_users إن لزم، وإلا assignee
      try {
        final accessible =
            await api.getTasksByUserAccess(employeeId, forceRefresh: true);
        if (accessible.isNotEmpty) return accessible;
      } catch (_) {}
      return await api.getTasksByAssignee(employeeId, forceRefresh: true);
    } catch (e) {
      debugPrint('خطأ في تحميل مهام الموظف $employeeId: $e');
      return [];
    }
  }

  /// قسم تاريخ العمل الخاص بالموظف
  Widget _buildEmployeeWorkHistorySection(BuildContext context, Employee employee) {

    return _buildSection(
      title: 'تاريخ العمل والخبرات المهنية',
      icon: Icons.work_history,
      children: [
        FutureBuilder<List<WorkHistory>>(
          future: _loadEmployeeWorkHistory(employee.id),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Padding(
                padding: EdgeInsets.all(8.0),
                child: LinearProgressIndicator(),
              );
            }
            if (snapshot.hasError) {
              return Text('حدث خطأ أثناء تحميل تاريخ العمل: ${snapshot.error}',
                  style: const TextStyle(color: Colors.red));
            }
            final workHistory = snapshot.data ?? [];
            if (workHistory.isEmpty) {
              return const Text('لا يوجد تاريخ عمل مسجل لهذا الموظف');
            }

            // قائمة تاريخ العمل مبسطة
            return ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: workHistory.length.clamp(0, 3), // عرض 3 سجلات كحد أقصى هنا
              separatorBuilder: (_, __) => const Divider(height: 12),
              itemBuilder: (context, index) {
                final wh = workHistory[index];
                final isCurrentJob = wh.isCurrent;
                final statusColor = isCurrentJob ? Colors.green : Colors.grey;

                return ListTile(
                  contentPadding: EdgeInsets.zero,
                  title: Text(wh.jobTitle, maxLines: 1, overflow: TextOverflow.ellipsis),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('${wh.companyName} • ${wh.organizationTypeArabic}'),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(Icons.schedule, size: 14, color: Colors.grey[600]),
                          const SizedBox(width: 4),
                          Text('${wh.durationText} • ${isCurrentJob ? "الوظيفة الحالية" : "وظيفة سابقة"}'),
                        ],
                      ),
                    ],
                  ),
                  leading: CircleAvatar(
                    radius: 16,
                    backgroundColor: statusColor.withValues(alpha: 0.15),
                    child: Icon(
                      isCurrentJob ? Icons.work : Icons.work_history,
                      color: statusColor,
                      size: 18,
                    ),
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.open_in_new),
                    tooltip: 'عرض التفاصيل',
                    onPressed: () {
                      HRNavigator.toWorkHistoryDetails(wh.id);
                    },
                  ),
                );
              },
            );
          },
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextButton.icon(
                onPressed: () {
                  // انتقال إلى شاشة تاريخ العمل مع فلتر الموظف
                  HRNavigator.toEmployeeWorkHistory(employee.id);
                },
                icon: const Icon(Icons.list),
                label: const Text('عرض تاريخ العمل كاملاً'),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  // انتقال لإضافة سجل عمل جديد لهذا الموظف
                  HRNavigator.toAddWorkHistory();
                },
                icon: const Icon(Icons.add),
                label: const Text('إضافة سجل عمل'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future<List<WorkHistory>> _loadEmployeeWorkHistory(int employeeId) async {
    try {
      final api = WorkHistoryApiService();
      return await api.getEmployeeWorkHistory(employeeId);
    } catch (e) {
      debugPrint('خطأ في تحميل تاريخ عمل الموظف $employeeId: $e');
      return [];
    }
  }

  /// قسم السلف والمساعدات المالية للموظف
  Widget _buildEmployeeAdvancesSection(BuildContext context, Employee employee) {
    return _buildSection(
      title: 'السلف والمساعدات المالية',
      icon: Icons.account_balance_wallet,
      children: [
        FutureBuilder<List<EmployeeAdvance>>(
          future: _loadEmployeeAdvances(employee.id),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Padding(
                padding: EdgeInsets.all(8.0),
                child: LinearProgressIndicator(),
              );
            }
            if (snapshot.hasError) {
              return Text('حدث خطأ أثناء تحميل السلف: ${snapshot.error}',
                  style: const TextStyle(color: Colors.red));
            }
            final advances = snapshot.data ?? [];
            if (advances.isEmpty) {
              return const Text('لا توجد سلف أو مساعدات مالية لهذا الموظف');
            }

            // قائمة السلف مبسطة
            return ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: advances.length.clamp(0, 4), // عرض 4 سلف كحد أقصى هنا
              separatorBuilder: (_, __) => const Divider(height: 12),
              itemBuilder: (context, index) {
                final advance = advances[index];
                final statusColor = _getAdvanceStatusColor(advance.status);
                final completionPercentage = advance.completionPercentage;

                return ListTile(
                  contentPadding: EdgeInsets.zero,
                  title: Text(advance.advanceTypeArabic, maxLines: 1, overflow: TextOverflow.ellipsis),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('المبلغ: ${advance.amount.toStringAsFixed(0)} • المتبقي: ${advance.remainingAmount.toStringAsFixed(0)}'),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(Icons.timeline, size: 14, color: Colors.grey[600]),
                          const SizedBox(width: 4),
                          Text('${advance.statusArabic} • مكتمل ${completionPercentage.toStringAsFixed(0)}%'),
                        ],
                      ),
                      if (completionPercentage > 0 && completionPercentage < 100) ...[
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: completionPercentage / 100,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                        ),
                      ],
                    ],
                  ),
                  leading: CircleAvatar(
                    radius: 16,
                    backgroundColor: statusColor.withValues(alpha: 0.15),
                    child: Icon(
                      _getAdvanceIcon(advance.advanceType),
                      color: statusColor,
                      size: 18,
                    ),
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.open_in_new),
                    tooltip: 'عرض التفاصيل',
                    onPressed: () {
                      HRNavigator.toAdvanceDetails(advance.id);
                    },
                  ),
                );
              },
            );
          },
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextButton.icon(
                onPressed: () {
                  // انتقال إلى شاشة سلف الموظف
                  _navigateToEmployeeAdvances(employee.id);
                },
                icon: const Icon(Icons.list),
                label: const Text('عرض جميع السلف'),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  // انتقال لإضافة سلفة جديدة لهذا الموظف
                  HRNavigator.toAddAdvance();
                },
                icon: const Icon(Icons.add),
                label: const Text('طلب سلفة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future<List<EmployeeAdvance>> _loadEmployeeAdvances(int employeeId) async {
    try {
      final api = AdvanceApiService();
      return await api.getEmployeeAdvances(employeeId);
    } catch (e) {
      debugPrint('خطأ في تحميل سلف الموظف $employeeId: $e');
      return [];
    }
  }

  Color _getAdvanceStatusColor(String status) {
    switch (status) {
      case 'approved':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'rejected':
        return Colors.red;
      case 'completed':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  IconData _getAdvanceIcon(String advanceType) {
    switch (advanceType) {
      case 'salary_advance':
        return Icons.payments;
      case 'financial_assistance':
        return Icons.volunteer_activism;
      case 'loan':
        return Icons.account_balance;
      default:
        return Icons.account_balance_wallet;
    }
  }

  void _navigateToEmployeeAdvances(int employeeId) {
    HRNavigator.toEmployeeAdvances(employeeId);
  }

  /// قسم الإجازات للموظف
  Widget _buildEmployeeLeavesSection(BuildContext context, Employee employee) {
    return _buildSection(
      title: 'الإجازات والعطل',
      icon: Icons.event_busy,
      children: [
        FutureBuilder<List<EmployeeLeave>>(
          future: _loadEmployeeLeaves(employee.id),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Padding(
                padding: EdgeInsets.all(8.0),
                child: LinearProgressIndicator(),
              );
            }
            if (snapshot.hasError) {
              return Text('حدث خطأ أثناء تحميل الإجازات: ${snapshot.error}',
                  style: const TextStyle(color: Colors.red));
            }
            final leaves = snapshot.data ?? [];
            if (leaves.isEmpty) {
              return const Text('لا توجد إجازات مسجلة لهذا الموظف');
            }

            // قائمة الإجازات مبسطة
            return ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: leaves.length.clamp(0, 4), // عرض 4 إجازات كحد أقصى هنا
              separatorBuilder: (_, __) => const Divider(height: 12),
              itemBuilder: (context, index) {
                final leave = leaves[index];
                final statusColor = _getLeaveStatusColor(leave.status);
                final isActive = leave.isActive;
                final isUpcoming = leave.isUpcoming;

                return ListTile(
                  contentPadding: EdgeInsets.zero,
                  title: Text(leave.leaveTypeArabic, maxLines: 1, overflow: TextOverflow.ellipsis),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('${_formatDate(leave.startDate)} - ${_formatDate(leave.endDate)} (${leave.totalDays} أيام)'),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            isActive ? Icons.play_circle : isUpcoming ? Icons.schedule : Icons.check_circle,
                            size: 14,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${leave.statusArabic}${isActive ? ' • نشطة حالياً' : isUpcoming ? ' • قادمة' : leave.isCompleted ? ' • منتهية' : ''}',
                          ),
                        ],
                      ),
                      if (leave.reason?.isNotEmpty == true) ...[
                        const SizedBox(height: 2),
                        Text(
                          'السبب: ${leave.reason}',
                          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                  leading: CircleAvatar(
                    radius: 16,
                    backgroundColor: statusColor.withValues(alpha: 0.15),
                    child: Icon(
                      _getLeaveIcon(leave.leaveType, leave.status),
                      color: statusColor,
                      size: 18,
                    ),
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.open_in_new),
                    tooltip: 'عرض التفاصيل',
                    onPressed: () {
                      _navigateToLeaveDetails(leave.id);
                    },
                  ),
                );
              },
            );
          },
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextButton.icon(
                onPressed: () {
                  // انتقال إلى شاشة إجازات الموظف
                  HRNavigator.toEmployeeLeaves(employee.id);
                },
                icon: const Icon(Icons.list),
                label: const Text('عرض جميع الإجازات'),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  // انتقال لطلب إجازة جديدة
                  _navigateToAddLeave();
                },
                icon: const Icon(Icons.add),
                label: const Text('طلب إجازة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future<List<EmployeeLeave>> _loadEmployeeLeaves(int employeeId) async {
    try {
      final api = LeaveApiService();
      return await api.getEmployeeLeaves(employeeId);
    } catch (e) {
      debugPrint('خطأ في تحميل إجازات الموظف $employeeId: $e');
      return [];
    }
  }

  Color _getLeaveStatusColor(String status) {
    switch (status) {
      case 'approved':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'rejected':
        return Colors.red;
      case 'cancelled':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  IconData _getLeaveIcon(String leaveType, String status) {
    if (status == 'rejected') return Icons.cancel;
    if (status == 'cancelled') return Icons.block;

    switch (leaveType) {
      case 'annual':
        return Icons.beach_access;
      case 'sick':
        return Icons.local_hospital;
      case 'emergency':
        return Icons.emergency;
      case 'maternity':
        return Icons.child_care;
      case 'unpaid':
        return Icons.money_off;
      default:
        return Icons.event_busy;
    }
  }

  void _navigateToLeaveDetails(int leaveId) {
    // استخدام المسار المباشر لأن HRNavigator قد لا يحتوي على toLeaveDetails
    Get.toNamed('/hr/leaves/$leaveId/details');
  }

  void _navigateToAddLeave() {
    Get.toNamed('/hr/leaves/add');
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
  }
}
