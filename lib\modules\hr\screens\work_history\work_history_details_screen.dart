import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/hr_controllers.dart';
import '../../models/hr_models.dart';
import '../../../../constants/app_colors.dart';
import '../../../../constants/app_styles.dart';
import '../../../../widgets/loading_indicator.dart';

/// شاشة تفاصيل سجل العمل
class WorkHistoryDetailsScreen extends StatefulWidget {
  const WorkHistoryDetailsScreen({super.key});

  @override
  State<WorkHistoryDetailsScreen> createState() => _WorkHistoryDetailsScreenState();
}

class _WorkHistoryDetailsScreenState extends State<WorkHistoryDetailsScreen> {
  late WorkHistoryController controller;
  int? workHistoryId;

  @override
  void initState() {
    super.initState();
    
    // تسجيل المتحكم إذا لم يكن مُسجلاً
    if (!Get.isRegistered<WorkHistoryController>()) {
      Get.lazyPut(() => WorkHistoryController());
    }
    controller = Get.find<WorkHistoryController>();

    // الحصول على معرف سجل العمل من المسار
    workHistoryId = int.tryParse(Get.parameters['id'] ?? '');
    
    if (workHistoryId != null) {
      // تحميل تفاصيل سجل العمل
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.getWorkHistoryDetails(workHistoryId!);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل سجل العمل'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          // زر التعديل
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _navigateToEdit(),
          ),
          // زر الحذف
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => _showDeleteDialog(),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const LoadingIndicator();
        }

        if (controller.error.value.isNotEmpty) {
          return _buildErrorState();
        }

        final workHistory = controller.selectedWorkHistory.value;
        if (workHistory == null) {
          return _buildNotFoundState();
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // بطاقة المعلومات الأساسية
              _buildBasicInfoCard(workHistory),
              const SizedBox(height: 16),
              
              // بطاقة التواريخ
              _buildDatesCard(workHistory),
              const SizedBox(height: 16),
              
              // بطاقة معلومات إضافية
              _buildAdditionalInfoCard(workHistory),
              const SizedBox(height: 16),
              
              // بطاقة الإحصائيات
              _buildStatisticsCard(workHistory),
              const SizedBox(height: 16),
              
              // أزرار الإجراءات
              _buildActionButtons(workHistory),
            ],
          ),
        );
      }),
    );
  }

  /// بناء بطاقة المعلومات الأساسية
  Widget _buildBasicInfoCard(WorkHistory workHistory) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'المعلومات الأساسية',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                const Spacer(),
                // حالة العمل
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: workHistory.isCurrent ? Colors.green : Colors.grey,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    workHistory.statusText,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // المنصب الوظيفي
            _buildDetailRow('المنصب الوظيفي', workHistory.jobTitle, Icons.work),
            const SizedBox(height: 12),
            
            // اسم الشركة
            _buildDetailRow('الشركة/المؤسسة', workHistory.companyName, Icons.business),
            const SizedBox(height: 12),
            
            // اسم الموظف
            if (workHistory.employeeName != null)
              _buildDetailRow('الموظف', workHistory.employeeName!, Icons.person),
            
            // رقم الموظف
            if (workHistory.employeeNumber != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow('رقم الموظف', workHistory.employeeNumber!, Icons.badge),
            ],
            
            // نوع المنظمة
            if (workHistory.organizationType != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow('نوع المنظمة', workHistory.organizationTypeArabic, Icons.category),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة التواريخ
  Widget _buildDatesCard(WorkHistory workHistory) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.date_range, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'التواريخ والمدة',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // تاريخ البداية
            _buildDetailRow('تاريخ البداية', _formatDate(workHistory.startDate), Icons.play_arrow),
            const SizedBox(height: 12),
            
            // تاريخ الانتهاء
            _buildDetailRow(
              'تاريخ الانتهاء', 
              workHistory.endDate != null ? _formatDate(workHistory.endDate!) : 'مستمر',
              Icons.stop,
            ),
            const SizedBox(height: 12),
            
            // مدة العمل
            _buildDetailRow('مدة العمل', workHistory.durationText, Icons.schedule),
            
            // تاريخ النقل
            if (workHistory.transferDate != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow('تاريخ النقل', _formatDate(workHistory.transferDate!), Icons.transfer_within_a_station),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة المعلومات الإضافية
  Widget _buildAdditionalInfoCard(WorkHistory workHistory) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.description, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'معلومات إضافية',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // سبب ترك العمل
            if (workHistory.leavingReason?.isNotEmpty == true) ...[
              _buildDetailRow('سبب ترك العمل', workHistory.leavingReason!, Icons.exit_to_app),
              const SizedBox(height: 12),
            ],
            
            // الملاحظات
            if (workHistory.notes?.isNotEmpty == true) ...[
              _buildDetailRow('الملاحظات', workHistory.notes!, Icons.note),
              const SizedBox(height: 12),
            ],
            
            // تاريخ الإنشاء
            _buildDetailRow(
              'تاريخ الإنشاء', 
              _formatTimestamp(workHistory.createdAt), 
              Icons.create,
            ),
            
            // تاريخ التحديث
            if (workHistory.updatedAt != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow(
                'تاريخ التحديث', 
                _formatTimestamp(workHistory.updatedAt!), 
                Icons.update,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الإحصائيات
  Widget _buildStatisticsCard(WorkHistory workHistory) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'إحصائيات',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'المدة بالأشهر',
                    workHistory.durationInMonths.toString(),
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    'المدة بالسنوات',
                    workHistory.durationInYears.toStringAsFixed(1),
                    Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons(WorkHistory workHistory) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _navigateToEdit(),
            icon: const Icon(Icons.edit),
            label: const Text('تعديل'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _showDeleteDialog(),
            icon: const Icon(Icons.delete),
            label: const Text('حذف'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء صف تفاصيل
  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          const Text(
            'حدث خطأ في تحميل البيانات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            controller.error.value,
            style: TextStyle(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              if (workHistoryId != null) {
                controller.getWorkHistoryDetails(workHistoryId!);
              }
            },
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء حالة عدم الوجود
  Widget _buildNotFoundState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.search_off, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          const Text(
            'سجل العمل غير موجود',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
          const SizedBox(height: 8),
          const Text(
            'قد يكون السجل محذوف أو غير متاح',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => Get.back(),
            child: const Text('العودة'),
          ),
        ],
      ),
    );
  }

  /// التنقل للتعديل
  void _navigateToEdit() {
    if (workHistoryId != null) {
      Get.toNamed('/hr/work-history/$workHistoryId/edit');
    }
  }

  /// عرض حوار الحذف
  void _showDeleteDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف سجل العمل هذا؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back(); // إغلاق الحوار
              if (workHistoryId != null) {
                final success = await controller.deleteWorkHistory(workHistoryId!);
                if (success) {
                  Get.back(); // العودة لقائمة تاريخ العمل
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// تنسيق الطابع الزمني
  String _formatTimestamp(int timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
