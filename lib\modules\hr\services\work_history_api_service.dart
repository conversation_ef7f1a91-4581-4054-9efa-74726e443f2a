import 'package:flutter/foundation.dart';
import '../../../services/api/base_api_service.dart';
import '../models/hr_models.dart';

/// خدمة API لتاريخ العمل والخبرات المهنية
class WorkHistoryApiService extends BaseApiService {
  static const String _baseEndpoint = '/api/EmployeeWorkHistory';

  /// الحصول على تاريخ العمل لموظف محدد
  Future<List<WorkHistory>> getEmployeeWorkHistory(int employeeId) async {
    try {
      final response = await get('$_baseEndpoint/employee/$employeeId');
      return handleListResponse(response, (json) => WorkHistory.fromJson(json));
    } catch (e) {
      throw Exception('خطأ في الحصول على تاريخ العمل: $e');
    }
  }

  /// الحصول على جميع سجلات تاريخ العمل مع الترقيم
  /// ملاحظة: الباك إند لا يدعم البحث المتقدم حالياً، سيتم تطبيق الفلترة محلياً
  Future<Map<String, dynamic>> getAllWorkHistory({
    int page = 1,
    int pageSize = 10,
    String? searchTerm,
    int? employeeId,
    String? organizationType,
    bool? isCurrent,
  }) async {
    try {
      List<WorkHistory> allWorkHistory = [];

      // إذا كان هناك موظف محدد، احصل على تاريخ عمله فقط
      if (employeeId != null) {
        allWorkHistory = await getEmployeeWorkHistory(employeeId);
      } else {
        // لا يوجد endpoint للحصول على جميع سجلات تاريخ العمل
        // إرجاع قائمة فارغة مع رسالة توضيحية
        debugPrint('⚠️ لا يوجد endpoint للحصول على جميع سجلات تاريخ العمل');
        return {
          'workHistory': <WorkHistory>[],
          'totalCount': 0,
          'currentPage': page,
          'totalPages': 1,
          'message': 'يرجى تحديد موظف للبحث في تاريخ عمله',
        };
      }

      // تطبيق الفلاتر محلياً
      var filteredHistory = allWorkHistory.where((wh) {
        bool matches = true;

        if (searchTerm?.isNotEmpty == true) {
          final term = searchTerm!.toLowerCase();
          matches = matches && (
            wh.jobTitle.toLowerCase().contains(term) ||
            wh.companyName.toLowerCase().contains(term) ||
            (wh.employeeName?.toLowerCase().contains(term) ?? false)
          );
        }

        if (organizationType?.isNotEmpty == true) {
          matches = matches && wh.organizationType == organizationType;
        }

        if (isCurrent != null) {
          matches = matches && wh.isCurrent == isCurrent;
        }

        return matches;
      }).toList();

      // تطبيق الترقيم
      final totalCount = filteredHistory.length;
      final totalPages = totalCount > 0 ? (totalCount / pageSize).ceil() : 1;
      final startIndex = (page - 1) * pageSize;
      final endIndex = (startIndex + pageSize).clamp(0, totalCount);

      if (startIndex < totalCount) {
        filteredHistory = filteredHistory.sublist(startIndex, endIndex);
      } else {
        filteredHistory = [];
      }

      return {
        'workHistory': filteredHistory,
        'totalCount': totalCount,
        'currentPage': page,
        'totalPages': totalPages,
      };
    } catch (e) {
      throw Exception('خطأ في الحصول على تاريخ العمل: $e');
    }
  }

  /// الحصول على تفاصيل سجل عمل محدد
  Future<WorkHistory> getWorkHistoryDetails(int id) async {
    try {
      final response = await get('$_baseEndpoint/$id');
      return handleResponse(response, (data) => WorkHistory.fromJson(data));
    } catch (e) {
      throw Exception('خطأ في الحصول على تفاصيل سجل العمل: $e');
    }
  }

  /// إنشاء سجل عمل جديد
  Future<WorkHistory> createWorkHistory(CreateWorkHistoryDto dto) async {
    try {
      final jsonData = dto.toJson();
      debugPrint('📤 إرسال بيانات سجل العمل إلى API:');
      debugPrint('URL: $_baseEndpoint');
      debugPrint('Data: $jsonData');

      final response = await post(_baseEndpoint, jsonData);

      debugPrint('📥 استجابة API:');
      debugPrint('Status: ${response.statusCode}');
      debugPrint('Body: ${response.body}');

      return handleResponse(response, (data) => WorkHistory.fromJson(data));
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء سجل العمل: $e');
      throw Exception('خطأ في إنشاء سجل العمل: $e');
    }
  }

  /// تحديث سجل العمل
  Future<WorkHistory> updateWorkHistory(int id, UpdateWorkHistoryDto dto) async {
    try {
      final response = await put('$_baseEndpoint/$id', dto.toJson());
      return handleResponse(response, (data) => WorkHistory.fromJson(data));
    } catch (e) {
      throw Exception('خطأ في تحديث سجل العمل: $e');
    }
  }

  /// حذف سجل العمل
  Future<void> deleteWorkHistory(int id) async {
    try {
      final response = await delete('$_baseEndpoint/$id');
      
      if (response.statusCode != 200) {
        throw Exception('فشل في حذف سجل العمل: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في حذف سجل العمل: $e');
    }
  }

  /// البحث في تاريخ العمل
  /// ملاحظة: يستخدم نفس منطق getAllWorkHistory حالياً
  Future<Map<String, dynamic>> searchWorkHistory(WorkHistorySearchDto searchDto) async {
    return await getAllWorkHistory(
      page: searchDto.page,
      pageSize: searchDto.pageSize,
      searchTerm: searchDto.searchTerm,
      employeeId: searchDto.employeeId,
      organizationType: searchDto.organizationType,
      isCurrent: searchDto.isCurrent,
    );
  }

  /// الحصول على إحصائيات تاريخ العمل
  /// ملاحظة: الباك إند لا يدعم هذا endpoint حالياً، سيتم حساب الإحصائيات محلياً
  Future<WorkHistoryStatisticsDto> getWorkHistoryStatistics() async {
    try {
      // TODO: إضافة endpoint للإحصائيات في الباك إند
      // مؤقتاً، أرجع إحصائيات فارغة
      return const WorkHistoryStatisticsDto(
        totalRecords: 0,
        currentJobs: 0,
        previousJobs: 0,
        averageJobDuration: 0.0,
        jobsByOrganizationType: {},
        jobsByCompany: {},
        jobsByTitle: {},
        topCompanies: [],
        longestJobs: [],
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات تاريخ العمل: $e');
    }
  }

  /// الحصول على إحصائيات تاريخ العمل لموظف محدد
  /// ملاحظة: سيتم حساب الإحصائيات محلياً من بيانات تاريخ العمل
  Future<Map<String, dynamic>> getEmployeeWorkStatistics(int employeeId) async {
    try {
      final workHistory = await getEmployeeWorkHistory(employeeId);

      // حساب الإحصائيات محلياً
      final currentJobs = workHistory.where((wh) => wh.isCurrent).length;
      final previousJobs = workHistory.where((wh) => !wh.isCurrent).length;
      final totalExperience = workHistory.fold<double>(0, (sum, wh) => sum + wh.durationInYears);

      return {
        'totalJobs': workHistory.length,
        'currentJobs': currentJobs,
        'previousJobs': previousJobs,
        'totalExperienceYears': totalExperience,
        'averageJobDuration': workHistory.isNotEmpty ? totalExperience / workHistory.length : 0.0,
      };
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات الموظف: $e');
    }
  }

  /// تصدير تاريخ العمل
  /// ملاحظة: الباك إند لا يدعم التصدير حالياً
  Future<String> exportWorkHistory({
    String format = 'excel', // excel, csv, pdf
    int? employeeId,
    String? organizationType,
    bool? isCurrent,
  }) async {
    try {
      // TODO: إضافة endpoint للتصدير في الباك إند
      throw UnimplementedError('وظيفة التصدير غير متاحة حالياً - سيتم إضافتها قريباً');
    } catch (e) {
      throw Exception('خطأ في تصدير البيانات: $e');
    }
  }

  /// الحصول على أنواع المنظمات المتاحة
  List<Map<String, String>> getOrganizationTypes() {
    return [
      {'value': 'government', 'label': 'حكومي', 'description': 'مؤسسة حكومية'},
      {'value': 'private', 'label': 'خاص', 'description': 'شركة خاصة'},
      {'value': 'ngo', 'label': 'منظمة غير ربحية', 'description': 'منظمة مجتمع مدني'},
      {'value': 'international', 'label': 'منظمة دولية', 'description': 'منظمة دولية أو أجنبية'},
      {'value': 'mixed', 'label': 'مختلط', 'description': 'قطاع مختلط'},
    ];
  }

  /// الحصول على أسباب ترك العمل الشائعة
  List<Map<String, String>> getLeavingReasons() {
    return [
      {'value': 'better_opportunity', 'label': 'فرصة أفضل', 'description': 'الحصول على فرصة عمل أفضل'},
      {'value': 'salary_increase', 'label': 'زيادة الراتب', 'description': 'للحصول على راتب أعلى'},
      {'value': 'career_growth', 'label': 'التطوير المهني', 'description': 'للتطوير والنمو المهني'},
      {'value': 'relocation', 'label': 'الانتقال', 'description': 'الانتقال لمكان آخر'},
      {'value': 'personal_reasons', 'label': 'أسباب شخصية', 'description': 'ظروف شخصية'},
      {'value': 'contract_end', 'label': 'انتهاء العقد', 'description': 'انتهاء مدة العقد'},
      {'value': 'retirement', 'label': 'التقاعد', 'description': 'الوصول لسن التقاعد'},
      {'value': 'termination', 'label': 'إنهاء الخدمة', 'description': 'إنهاء الخدمة من قبل الشركة'},
      {'value': 'other', 'label': 'أخرى', 'description': 'أسباب أخرى'},
    ];
  }
}
